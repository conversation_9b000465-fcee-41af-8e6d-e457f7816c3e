from typing import Dict, Any, List
from datetime import datetime
from bs4 import BeautifulSoup
import re

from app.core.logging_config import get_logger

logger = get_logger("parser")

def clean_text(text):
    """
    清理文本，移除特殊字符和多余空格

    Args:
        text: 要清理的文本

    Returns:
        清理后的文本
    """
    if not text:
        return ''

    # 移除特殊字符和多余空格
    cleaned = re.sub(r'[^\w\s\.\-\+]', '', text.strip())
    cleaned = re.sub(r'\s+', ' ', cleaned).strip()
    return cleaned

def convert_to_decimal_or_none(value, max_digits=5, decimal_places=2):
    """
    将字符串转换为数值或None，并确保在指定范围内

    Args:
        value: 要转换的字符串
        max_digits: 最大总位数
        decimal_places: 小数位数

    Returns:
        转换后的值：如果能转换为数值则返回数值，否则返回None
    """
    if not value or value.strip() == '' or value.strip() == '&nbsp;':
        return None

    # 清理文本，只保留数字、小数点和正负号
    cleaned_value = re.sub(r'[^\d\.\-\+]', '', value.strip())

    try:
        # 尝试转换为浮点数
        num = float(cleaned_value)

        # 计算最大允许值
        max_value = 10 ** (max_digits - decimal_places) - 10 ** (-decimal_places)

        # 如果超出范围，则返回None
        if num > max_value or num < -max_value:
            logger.warning(f"值 {num} 超出范围 [-{max_value}, {max_value}]，将被设置为None")
            return None

        # 四舍五入到指定小数位
        return round(num, decimal_places)
    except (ValueError, TypeError) as e:
        # 如果转换失败，返回None
        logger.warning(f"值 '{value}' 无法转换为数值: {e}")
        return None

def parse_basic_info(soup, school_id):
    """
    解析基本信息

    Args:
        soup: BeautifulSoup对象
        school_id: 学校ID

    Returns:
        basic_info: 基本信息字典
    """
    basic_info = {
        'school_id': school_id,
        'research_direction': '',
        'exam_range': '',
        'reference_books': '',
        'retest_content': '',
        'tuition_fee': '',
        'study_years': '',
        'accommodation': '',
        'admission_requirements': ''
    }

    # 获取所有li标签
    items = soup.find_all('li')
    for item in items:
        a_tag = item.find('a')
        if not a_tag:
            continue

        text = a_tag.text.strip()
        if '2025年研究方向' in text:
            basic_info['research_direction'] = item.find('h3').text.strip() if item.find('h3') else ''
        elif '2025年考试范围' in text:
            basic_info['exam_range'] = item.find('p').text.strip() if item.find('p') else ''
        elif '2025年初试参考图书' in text:
            basic_info['reference_books'] = item.find('p').text.strip() if item.find('p') else ''
        elif '2025年复试内容' in text:
            basic_info['retest_content'] = item.find('p').text.strip() if item.find('p') else ''
        elif '2025年学费学制住宿' in text:
            text_content = item.find('h3').text.strip() if item.find('h3') else ''
            if text_content:
                tuition_match = re.search(r'学费：(.*?)；', text_content)
                years_match = re.search(r'学制：(.*?)；', text_content)
                accommodation_match = re.search(r'住宿：(.*?)；', text_content)
                basic_info['tuition_fee'] = tuition_match.group(1) if tuition_match else ''
                basic_info['study_years'] = years_match.group(1) if years_match else ''
                basic_info['accommodation'] = accommodation_match.group(1) if accommodation_match else ''
        elif '2025年报考要求' in text:
            basic_info['admission_requirements'] = item.find('h3').text.strip() if item.find('h3') else ''

    logger.info(f"基本信息解析完成: ID={school_id}")
    return basic_info

def parse_admission_list(soup, school_id, year):
    """
    解析拟录取名单

    Args:
        soup: BeautifulSoup对象
        school_id: 学校ID
        year: 年份

    Returns:
        admission_list: 拟录取名单列表
    """
    admission_list = []
    
    # 根据年份选择对应的表格ID
    table_id = 'GridView1' if year == 2024 else 'GridView6' if year == 2023 else 'GridView8'
    table = soup.find('table', id=table_id)
    
    if not table:
        logger.warning(f"未找到{year}年拟录取名单表格: ID={school_id}")
        return admission_list

    rows = table.find_all('tr')[1:]  # 跳过表头
    for row in rows:
        cols = row.find_all('td')
        if len(cols) < 10:  # 确保有足够的列
            continue

        # 处理空值和特殊字符
        initial_score = cols[5].text.strip()
        if initial_score == '&nbsp;' or initial_score.isspace():
            initial_score = None

        retest_score = cols[6].text.strip()
        if retest_score == '&nbsp;' or retest_score.isspace():
            retest_score = None

        total_score = cols[7].text.strip()
        if total_score == '&nbsp;' or total_score.isspace():
            total_score = None

        student_remark = cols[9].text.strip()
        if student_remark == '&nbsp;' or student_remark.isspace():
            student_remark = None

        admission = {
            'school_id': school_id,
            'year': year,
            'name': clean_text(cols[1].text),
            'college': clean_text(cols[2].text),
            'major_code': clean_text(cols[3].text),
            'major_name': clean_text(cols[4].text),
            'initial_score': initial_score,
            'retest_score': retest_score,
            'total_score': total_score,
            'first_choice_school': clean_text(cols[8].text),
            'student_remark': student_remark
        }
        admission_list.append(admission)

    logger.info(f"{year}年拟录取名单解析完成: ID={school_id}, 数量={len(admission_list)}")
    return admission_list

def parse_retest_list(soup, school_id, year):
    """
    解析复试名单

    Args:
        soup: BeautifulSoup对象
        school_id: 学校ID
        year: 年份

    Returns:
        retest_list: 复试名单列表
    """
    retest_list = []
    
    # 根据年份选择对应的表格ID
    table_id = 'GridView2' if year == 2024 else 'GridView5' if year == 2023 else 'GridView7'
    table = soup.find('table', id=table_id)
    
    if not table:
        logger.warning(f"未找到{year}年复试名单表格: ID={school_id}")
        return retest_list

    rows = table.find_all('tr')[1:]  # 跳过表头
    for row in rows:
        cols = row.find_all('td')
        if len(cols) < 12:  # 确保有足够的列
            continue

        # 处理空值和特殊字符
        politics_score = cols[5].text.strip()
        if politics_score == '&nbsp;' or politics_score.isspace():
            politics_score = None

        english_score = cols[6].text.strip()
        if english_score == '&nbsp;' or english_score.isspace():
            english_score = None

        major1_score = cols[7].text.strip()
        if major1_score == '&nbsp;' or major1_score.isspace():
            major1_score = None

        major2_score = cols[8].text.strip()
        if major2_score == '&nbsp;' or major2_score.isspace():
            major2_score = None

        initial_score = cols[9].text.strip()
        if initial_score == '&nbsp;' or initial_score.isspace():
            initial_score = None

        retest = {
            'school_id': school_id,
            'year': year,
            'name': clean_text(cols[1].text),
            'college': clean_text(cols[2].text),
            'major_code': clean_text(cols[3].text),
            'major_name': clean_text(cols[4].text),
            'politics_score': politics_score,
            'english_score': english_score,
            'major1_score': major1_score,
            'major2_score': major2_score,
            'initial_score': initial_score,
            'volunteer_type': clean_text(cols[10].text),
            'admission_status': clean_text(cols[11].text) if len(cols) > 11 else ''
        }
        retest_list.append(retest)

    logger.info(f"{year}年复试名单解析完成: ID={school_id}, 数量={len(retest_list)}")
    return retest_list

def parse_transfer_info(soup, school_id, year):
    """
    解析调剂信息

    Args:
        soup: BeautifulSoup对象
        school_id: 学校ID
        year: 年份

    Returns:
        transfer_list: 调剂信息列表
    """
    transfer_list = []
    
    # 复试名单
    table1 = soup.find('table', id='GridView3')
    # 拟录取名单
    table2 = soup.find('table', id='GridView4')

    if table1:
        rows = table1.find_all('tr')[1:]
        for row in rows:
            cols = row.find_all('td')
            if len(cols) < 12:
                continue

            # 处理空值和特殊字符
            initial_score = cols[9].text.strip()
            if initial_score == '&nbsp;' or initial_score.isspace():
                initial_score = None

            transfer = {
                'school_id': school_id,
                'year': year,
                'name': clean_text(cols[0].text),
                'transfer_school': clean_text(cols[1].text),
                'college': clean_text(cols[2].text),
                'major_code': clean_text(cols[3].text),
                'major_name': clean_text(cols[4].text),
                'initial_score': initial_score,
                'apply_school': clean_text(cols[10].text),
                'apply_major': clean_text(cols[11].text)
            }
            transfer_list.append(transfer)

    if table2:
        rows = table2.find_all('tr')[1:]
        for row in rows:
            cols = row.find_all('td')
            if len(cols) < 8:
                continue

            # 处理空值和特殊字符
            initial_score = cols[5].text.strip()
            if initial_score == '&nbsp;' or initial_score.isspace():
                initial_score = None

            transfer = {
                'school_id': school_id,
                'year': year,
                'name': clean_text(cols[0].text),
                'transfer_school': clean_text(cols[1].text),
                'college': clean_text(cols[2].text),
                'major_code': clean_text(cols[3].text),
                'major_name': clean_text(cols[4].text),
                'initial_score': initial_score,
                'apply_school': clean_text(cols[6].text),
                'apply_major': clean_text(cols[7].text)
            }
            transfer_list.append(transfer)

    logger.info(f"{year}年调剂信息解析完成: ID={school_id}, 数量={len(transfer_list)}")
    return transfer_list

def parse_page_data(html_content: str, school_id: int) -> Dict[str, Any]:
    """
    解析页面数据

    Args:
        html_content: HTML页面内容
        school_id: 学校ID

    Returns:
        Dict[str, Any]: 解析后的数据
    """
    logger.info(f"开始解析页面数据: ID={school_id}")

    soup = BeautifulSoup(html_content, 'html.parser')

    data = {
        'admission_list': [],
        'retest_list': [],
        'basic_info': {},
        'transfer_info': []
    }

    # 获取当前年份
    current_year = datetime.now().year

    # 解析基本信息
    try:
        data['basic_info'] = parse_basic_info(soup, school_id)
    except Exception as e:
        logger.error(f"解析基本信息失败: ID={school_id}, 错误: {str(e)}")
        data['basic_info'] = {
            'school_id': school_id,
            'research_direction': '',
            'exam_range': '',
            'reference_books': '',
            'retest_content': '',
            'tuition_fee': '',
            'study_years': '',
            'accommodation': '',
            'admission_requirements': ''
        }

    # 解析2023-2025年的拟录取名单
    for year in [2023, 2024, 2025]:
        try:
            admission_list = parse_admission_list(soup, school_id, year)
            data['admission_list'].extend(admission_list)
        except Exception as e:
            logger.error(f"解析{year}年拟录取名单失败: ID={school_id}, 错误: {str(e)}")

    # 解析2023-2025年的复试名单
    for year in [2023, 2024, 2025]:
        try:
            retest_list = parse_retest_list(soup, school_id, year)
            data['retest_list'].extend(retest_list)
        except Exception as e:
            logger.error(f"解析{year}年复试名单失败: ID={school_id}, 错误: {str(e)}")

    # 解析2024年的调剂信息
    try:
        transfer_list = parse_transfer_info(soup, school_id, 2024)
        data['transfer_info'].extend(transfer_list)
    except Exception as e:
        logger.error(f"解析2024年调剂信息失败: ID={school_id}, 错误: {str(e)}")

    logger.info(f"页面数据解析完成: ID={school_id}")
    logger.info(f"解析结果统计: 录取名单={len(data['admission_list'])}, 复试名单={len(data['retest_list'])}, 调剂信息={len(data['transfer_info'])}")
    
    return data
