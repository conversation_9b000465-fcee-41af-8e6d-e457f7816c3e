import logging
import os
import sys
from concurrent_log_handler import ConcurrentRotatingFileHandler
from pathlib import Path

from app.core.config import settings

# 创建logs目录（如果不存在）
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

def setup_logging(log_level=logging.INFO):
    """
    设置日志配置

    Args:
        log_level: 日志级别，默认为INFO
    """
    # 创建日志格式
    log_format = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # 创建根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 清除现有的处理器
    if root_logger.handlers:
        root_logger.handlers.clear()

    # 添加控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(log_format)
    root_logger.addHandler(console_handler)

    # 添加文件处理器 - 使用支持并发访问的日志处理器
    # 按大小分割
    size_handler = ConcurrentRotatingFileHandler(
        filename=os.path.join("logs", "app.log"),
        maxBytes=settings.LOG_CONFIG["max_bytes"],
        backupCount=settings.LOG_CONFIG["backup_count"],
        encoding=settings.LOG_CONFIG["encoding"]
    )
    # 设置格式并添加处理器
    size_handler.setFormatter(log_format)
    root_logger.addHandler(size_handler)

    # 创建爬虫专用日志记录器
    crawler_logger = logging.getLogger("crawler")
    crawler_logger.setLevel(log_level)

    # 添加爬虫专用文件处理器 - 使用支持并发访问的日志处理器
    crawler_handler = ConcurrentRotatingFileHandler(
        filename=os.path.join("logs", "crawler.log"),
        maxBytes=settings.LOG_CONFIG["max_bytes"],
        backupCount=settings.LOG_CONFIG["backup_count"],
        encoding=settings.LOG_CONFIG["encoding"]
    )
    # 设置格式并添加处理器
    crawler_handler.setFormatter(log_format)
    crawler_logger.addHandler(crawler_handler)

    # 创建数据库专用日志记录器
    db_logger = logging.getLogger("db")
    db_logger.setLevel(log_level)

    # 添加数据库专用文件处理器 - 使用支持并发访问的日志处理器
    db_handler = ConcurrentRotatingFileHandler(
        filename=os.path.join("logs", "db.log"),
        maxBytes=settings.LOG_CONFIG["max_bytes"],
        backupCount=settings.LOG_CONFIG["backup_count"],
        encoding=settings.LOG_CONFIG["encoding"]
    )
    # 设置格式并添加处理器
    db_handler.setFormatter(log_format)
    db_logger.addHandler(db_handler)

    # 创建API专用日志记录器
    api_logger = logging.getLogger("api")
    api_logger.setLevel(log_level)

    # 添加API专用文件处理器 - 使用支持并发访问的日志处理器
    api_handler = ConcurrentRotatingFileHandler(
        filename=os.path.join("logs", "api.log"),
        maxBytes=settings.LOG_CONFIG["max_bytes"],
        backupCount=settings.LOG_CONFIG["backup_count"],
        encoding=settings.LOG_CONFIG["encoding"]
    )
    # 设置格式并添加处理器
    api_handler.setFormatter(log_format)
    api_logger.addHandler(api_handler)

    # 创建后台任务专用日志记录器
    background_logger = logging.getLogger("background")
    background_logger.setLevel(log_level)

    # 添加后台任务专用文件处理器 - 使用支持并发访问的日志处理器
    bg_handler = ConcurrentRotatingFileHandler(
        filename=os.path.join("logs", "background.log"),
        maxBytes=settings.LOG_CONFIG["max_bytes"],
        backupCount=settings.LOG_CONFIG["backup_count"],
        encoding=settings.LOG_CONFIG["encoding"]
    )
    # 设置格式并添加处理器
    bg_handler.setFormatter(log_format)
    background_logger.addHandler(bg_handler)

    return root_logger

# 获取各种日志记录器的函数
def get_logger(name=None):
    """获取指定名称的日志记录器"""
    return logging.getLogger(name)

def get_crawler_logger():
    """获取爬虫专用日志记录器"""
    return logging.getLogger("crawler")

def get_db_logger():
    """获取数据库专用日志记录器"""
    return logging.getLogger("db")

def get_api_logger():
    """获取API专用日志记录器"""
    return logging.getLogger("api")

def get_background_logger():
    """获取后台任务专用日志记录器"""
    return logging.getLogger("background")
