import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager

from app.core.config import settings
from app.core.logging_config import get_db_logger

logger = get_db_logger()

def get_db_connection():
    """
    创建数据库连接
    
    Returns:
        pymysql.Connection: 数据库连接对象
    """
    try:
        connection = pymysql.connect(
            host=settings.MYSQL["host"],
            port=settings.MYSQL["port"],
            user=settings.MYSQL["user"],
            password=settings.MYSQL["password"],
            db=settings.MYSQL["db"],
            charset=settings.MYSQL["charset"],
            cursorclass=DictCursor
        )
        logger.debug("数据库连接创建成功")
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise

@contextmanager
def db_cursor():
    """
    数据库游标上下文管理器，自动处理连接和事务
    
    Yields:
        pymysql.cursors.DictCursor: 数据库游标对象
    """
    connection = get_db_connection()
    try:
        with connection.cursor() as cursor:
            logger.debug("数据库游标创建成功")
            yield cursor
            connection.commit()
            logger.debug("数据库事务提交成功")
    except Exception as e:
        connection.rollback()
        logger.error(f"数据库操作失败，事务回滚: {str(e)}")
        raise
    finally:
        connection.close()
        logger.debug("数据库连接关闭")
