#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的备份表保存功能
"""

import requests
import json
import time
from datetime import datetime

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_fixed_backup_function():
    """测试修复后的备份功能"""
    
    # 测试单个学校
    test_data = {
        "ids": [19402]  # 测试一个学校
    }
    
    print("=" * 80)
    print("测试修复后的备份表保存功能")
    print("=" * 80)
    
    current_year = datetime.now().year
    print(f"当前年份: {current_year}")
    
    if current_year == 2025:
        print("应该爬取:")
        print("- GridView7: 2025年复试名单 -> ba_retest_list_backup")
        print("- GridView8: 2025年拟录取名单 -> ba_admission_list_backup")
    
    try:
        print(f"\n发送请求到: {BASE_URL}/crawl_school_info_sync_limited")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=300
        )
        end_time = time.time()
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"耗时: {end_time - start_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print("\n响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 分析结果
            if "results" in result:
                results = result["results"]
                for result_item in results:
                    school_id = result_item.get('school_id')
                    status = result_item.get('status')
                    message = result_item.get('message')
                    data_summary = result_item.get('data_summary')
                    
                    print(f"\n学校 {school_id} 处理结果:")
                    print(f"  状态: {status}")
                    print(f"  消息: {message}")
                    print(f"  数据摘要: {data_summary}")
                    
                    if status == "success":
                        print("  ✓ 爬取成功！")
                        if data_summary:
                            admission_info = data_summary.get('admission_count', '未知')
                            retest_info = data_summary.get('retest_count', '未知')
                            print(f"    录取名单: {admission_info}")
                            print(f"    复试名单: {retest_info}")
                    elif status == "skipped":
                        print("  ⚠ 被跳过，可能已经爬取过")
                    else:
                        print("  ✗ 爬取失败")
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"发生错误: {str(e)}")

def show_sql_check_commands():
    """显示检查数据的SQL命令"""
    print("\n" + "=" * 80)
    print("检查备份表数据的SQL命令")
    print("=" * 80)
    
    print("请在数据库中执行以下SQL命令检查数据：")
    print()
    print("1. 检查拟录取名单备份表:")
    print("   SELECT COUNT(*) as total_count FROM ba_admission_list_backup;")
    print("   SELECT school_id, year, COUNT(*) as count FROM ba_admission_list_backup GROUP BY school_id, year;")
    print("   SELECT * FROM ba_admission_list_backup WHERE school_id = 19402 ORDER BY id DESC LIMIT 5;")
    print()
    print("2. 检查复试名单备份表:")
    print("   SELECT COUNT(*) as total_count FROM ba_retest_list_backup;")
    print("   SELECT school_id, year, COUNT(*) as count FROM ba_retest_list_backup GROUP BY school_id, year;")
    print("   SELECT * FROM ba_retest_list_backup WHERE school_id = 19402 ORDER BY id DESC LIMIT 5;")
    print()
    print("3. 检查最新插入的数据:")
    print("   SELECT * FROM ba_admission_list_backup WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);")
    print("   SELECT * FROM ba_retest_list_backup WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);")

def show_fix_summary():
    """显示修复内容总结"""
    print("\n" + "=" * 80)
    print("修复内容总结")
    print("=" * 80)
    
    print("1. 修复的问题:")
    print("   - parse_admission_list 函数中年份到表格ID的映射不清晰")
    print("   - parse_retest_list 函数中年份到表格ID的映射不清晰")
    print("   - 缺少详细的调试日志")
    print()
    print("2. 修复的内容:")
    print("   - 使用明确的字典映射年份到表格ID")
    print("   - 添加年份配置检查和警告日志")
    print("   - 增加数据解析结果的详细日志")
    print("   - 添加数据保存成功/失败的日志")
    print()
    print("3. 年份映射:")
    print("   拟录取名单:")
    print("   - 2023年: GridView6")
    print("   - 2024年: GridView1") 
    print("   - 2025年: GridView8")
    print()
    print("   复试名单:")
    print("   - 2023年: GridView5")
    print("   - 2024年: GridView2")
    print("   - 2025年: GridView7")
    print()
    print("4. 预期结果:")
    print("   - 当前是2025年，应该能正确解析GridView7和GridView8")
    print("   - 数据应该保存到ba_admission_list_backup和ba_retest_list_backup表")
    print("   - 日志中应该显示解析和保存的详细信息")

def test_multiple_schools():
    """测试多个学校的处理"""
    print("\n" + "=" * 80)
    print("测试多个学校的多线程处理")
    print("=" * 80)
    
    test_data = {
        "ids": [19402, 19403]  # 测试两个学校
    }
    
    try:
        print(f"发送请求: {json.dumps(test_data, ensure_ascii=False)}")
        
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=600
        )
        end_time = time.time()
        
        print(f"响应状态码: {response.status_code}")
        print(f"总耗时: {end_time - start_time:.2f} 秒")
        print(f"平均每个学校: {(end_time - start_time)/len(test_data['ids']):.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            if "results" in result:
                results = result["results"]
                success_count = sum(1 for r in results if r.get("status") == "success")
                print(f"成功处理: {success_count}/{len(results)} 个学校")
                
                for i, result_item in enumerate(results):
                    school_id = result_item.get('school_id')
                    status = result_item.get('status')
                    print(f"学校{i+1} (ID:{school_id}): {status}")
        
    except Exception as e:
        print(f"多学校测试错误: {str(e)}")

if __name__ == "__main__":
    print("开始测试修复后的备份表保存功能...")
    
    # 显示修复总结
    show_fix_summary()
    
    # 测试单个学校
    test_fixed_backup_function()
    
    # 显示检查SQL
    show_sql_check_commands()
    
    # 测试多个学校
    test_multiple_schools()
    
    print("\n测试完成!")
    print("\n重要提醒:")
    print("1. 检查服务器日志，查看详细的处理过程")
    print("2. 执行SQL命令检查备份表中的数据")
    print("3. 如果仍然没有数据，可能是网页结构发生了变化")
    print("4. 可以对比原版API的处理结果")
