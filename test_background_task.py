import requests
import json
import time

# API地址
base_url = "http://localhost:8000/api"

def test_create_task():
    """测试创建任务"""
    print("测试创建任务...")
    
    # 创建任务
    response = requests.post(
        f"{base_url}/crawl_school_info",
        json={"ids": [3000 , 3001, 3002]}  # 使用ba_school_info表中detail_url_status为0的学校ID
    )
    
    # 打印结果
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(json.dumps(result, indent=4, ensure_ascii=False))
    
    return result.get("task_id")

def test_get_task_status(task_id):
    """测试获取任务状态"""
    print(f"\n测试获取任务状态: {task_id}")
    
    # 获取任务状态
    response = requests.get(f"{base_url}/task/{task_id}")
    
    # 打印结果
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(json.dumps(result, indent=4, ensure_ascii=False))
    
    return result.get("status")

def test_list_tasks():
    """测试获取任务列表"""
    print("\n测试获取任务列表...")
    
    # 获取任务列表
    response = requests.get(f"{base_url}/tasks")
    
    # 打印结果
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(json.dumps(result, indent=4, ensure_ascii=False))

def main():
    """主函数"""
    # 创建任务
    task_id = test_create_task()
    
    if not task_id:
        print("创建任务失败")
        return
    
    # 循环获取任务状态，直到任务完成或失败
    status = None
    while status not in ["completed", "failed"]:
        status = test_get_task_status(task_id)
        
        if status not in ["completed", "failed"]:
            print("任务正在处理中，等待5秒...")
            time.sleep(5)
    
    # 获取任务列表
    test_list_tasks()

if __name__ == "__main__":
    main()
