from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, Any, List
import uuid
import time

from app.models.schemas import (
    SchoolInfoRequest, SchoolInfoResponse,
    TaskCreateResponse, TaskStatusResponse, TaskListResponse,
    LoginStatusResponse
)
from app.services.background_tasks import TaskManager, start_worker
from app.services.crawler import is_session_valid, login, last_login_time
from app.services.processor import process_school_info_batch, process_school_info_batch_limited
from app.core.logging_config import get_api_logger

logger = get_api_logger()

# 登录状态变量
login_in_progress = False

# 创建路由
router = APIRouter()

@router.post("/crawl_school_info", response_model=TaskCreateResponse)
async def crawl_school_info(request: SchoolInfoRequest) -> Dict[str, Any]:
    """
    爬取学校信息API - 创建后台任务

    Args:
        request: 包含学校ID列表的请求

    Returns:
        Dict[str, Any]: 任务创建结果
    """
    try:
        logger.info(f"接收到爬取学校信息请求: IDs={request.ids}")

        # 确保后台工作线程已启动
        start_worker()

        # 创建任务ID
        task_id = str(uuid.uuid4())

        # 创建后台任务
        task_info = TaskManager.create_task(task_id, request.ids)

        logger.info(f"创建后台任务成功: ID={task_id}, 学校数量={len(request.ids)}")

        return {
            "task_id": task_id,
            "status": task_info["status"],
            "message": "任务已创建，正在后台处理",
            "total": len(request.ids)
        }

    except Exception as e:
        logger.error(f"创建爬取任务时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")

@router.post("/crawl_school_info_sync", response_model=SchoolInfoResponse)
async def crawl_school_info_sync(request: SchoolInfoRequest) -> Dict[str, Any]:
    """
    同步爬取学校信息API - 直接返回结果

    Args:
        request: 包含学校ID列表的请求

    Returns:
        Dict[str, Any]: 爬取结果
    """
    try:
        logger.info(f"接收到同步爬取学校信息请求: IDs={request.ids}")

        # 检查登录状态，如果未登录则先登录
        if not is_session_valid():
            logger.info("当前会话无效，开始执行登录")
            success, message = login()
            if not success:
                logger.error(f"登录失败: {message}")
                raise HTTPException(status_code=401, detail=f"登录失败: {message}")
            logger.info("登录成功")

        # 同步处理学校信息
        logger.info(f"开始同步处理学校信息: 学校数量={len(request.ids)}")
        results = process_school_info_batch(request.ids)

        logger.info(f"同步爬取完成: 共处理{len(results)}个学校")

        # 统计结果
        success_count = sum(1 for r in results if r["status"] == "success")
        skipped_count = sum(1 for r in results if r["status"] == "skipped")
        error_count = sum(1 for r in results if r["status"] == "error")

        logger.info(f"处理结果统计: 成功={success_count}, 跳过={skipped_count}, 失败={error_count}")

        return {
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步爬取学校信息时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"同步爬取失败: {str(e)}")

@router.post("/crawl_school_info_sync_limited", response_model=SchoolInfoResponse)
async def crawl_school_info_sync_limited(request: SchoolInfoRequest) -> Dict[str, Any]:
    """
    同步爬取学校信息API（限制版本）- 只爬取当年的拟录取名单和复试名单，超过一页只爬第一页和最后一页，保存到备份表

    Args:
        request: 包含学校ID列表的请求

    Returns:
        Dict[str, Any]: 爬取结果
    """
    try:
        logger.info(f"接收到同步爬取学校信息请求（限制版本）: IDs={request.ids}")

        # 检查登录状态，如果未登录则先登录
        if not is_session_valid():
            logger.info("当前会话无效，开始执行登录")
            success, message = login()
            if not success:
                logger.error(f"登录失败: {message}")
                raise HTTPException(status_code=401, detail=f"登录失败: {message}")
            logger.info("登录成功")

        # 同步处理学校信息（限制版本）
        logger.info(f"开始同步处理学校信息（限制版本）: 学校数量={len(request.ids)}")
        results = process_school_info_batch_limited(request.ids)

        logger.info(f"同步爬取完成（限制版本）: 共处理{len(results)}个学校")

        # 统计结果
        success_count = sum(1 for r in results if r["status"] == "success")
        skipped_count = sum(1 for r in results if r["status"] == "skipped")
        error_count = sum(1 for r in results if r["status"] == "error")

        logger.info(f"处理结果统计（限制版本）: 成功={success_count}, 跳过={skipped_count}, 失败={error_count}")

        return {
            "results": results
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步爬取学校信息时发生错误（限制版本）: {str(e)}")
        raise HTTPException(status_code=500, detail=f"同步爬取失败（限制版本）: {str(e)}")

@router.get("/task/{task_id}", response_model=TaskStatusResponse)
async def get_task_status(task_id: str) -> Dict[str, Any]:
    """
    获取任务状态

    Args:
        task_id: 任务ID

    Returns:
        Dict[str, Any]: 任务状态
    """
    try:
        # 获取任务状态
        task_info = TaskManager.get_task_status(task_id)

        if not task_info:
            raise HTTPException(status_code=404, detail=f"任务不存在: {task_id}")

        # 计算任务进度
        progress = 0
        if task_info["total"] > 0:
            progress = (task_info["processed"] / task_info["total"]) * 100

        # 计算任务运行时间
        elapsed_time = 0
        if task_info["end_time"]:
            elapsed_time = task_info["end_time"] - task_info["start_time"]
        else:
            elapsed_time = time.time() - task_info["start_time"]

        return {
            "task_id": task_id,
            "status": task_info["status"],
            "total": task_info["total"],
            "processed": task_info["processed"],
            "success": task_info["success"],
            "skipped": task_info["skipped"],
            "error": task_info["error"],
            "progress": progress,
            "elapsed_time": elapsed_time,
            "results": task_info["results"] if task_info["status"] == "completed" else []
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务状态失败: {str(e)}")

@router.get("/tasks", response_model=TaskListResponse)
async def list_tasks() -> Dict[str, List[Dict[str, Any]]]:
    """
    获取所有任务

    Returns:
        Dict[str, List[Dict[str, Any]]]: 所有任务信息
    """
    try:
        # 获取所有任务
        all_tasks = TaskManager.get_all_tasks()

        # 转换为列表格式
        task_list = []
        for task_id, task_info in all_tasks.items():
            # 计算任务进度
            progress = 0
            if task_info["total"] > 0:
                progress = (task_info["processed"] / task_info["total"]) * 100

            # 计算任务运行时间
            elapsed_time = 0
            if task_info["end_time"]:
                elapsed_time = task_info["end_time"] - task_info["start_time"]
            else:
                elapsed_time = time.time() - task_info["start_time"]

            task_list.append({
                "task_id": task_id,
                "status": task_info["status"],
                "total": task_info["total"],
                "processed": task_info["processed"],
                "success": task_info["success"],
                "skipped": task_info["skipped"],
                "error": task_info["error"],
                "progress": progress,
                "elapsed_time": elapsed_time
            })

        return {"tasks": task_list}

    except Exception as e:
        logger.error(f"获取任务列表时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")

def background_login():
    """
    后台执行登录操作
    """
    global login_in_progress
    try:
        logger.info("后台任务开始执行登录")
        success, _ = login()
        if success:
            logger.info("后台登录成功")
        else:
            logger.error("后台登录失败")
    except Exception as e:
        logger.error(f"后台登录过程中发生错误: {str(e)}")
    finally:
        login_in_progress = False
        logger.info("后台登录任务完成")

@router.get("/check_login_status", response_model=LoginStatusResponse)
async def check_login_status(background_tasks: BackgroundTasks) -> Dict[str, Any]:
    """
    检查当前登录状态，如果未登录则在后台自动执行登录

    Args:
        background_tasks: FastAPI后台任务对象

    Returns:
        Dict[str, Any]: 登录状态信息
    """
    global login_in_progress

    try:
        logger.info("接收到检查登录状态请求")

        # 检查当前会话是否有效
        if is_session_valid():
            # 会话有效，返回登录状态
            login_time_str = last_login_time.strftime("%Y-%m-%d %H:%M:%S") if last_login_time else None
            logger.info("当前会话有效，无需重新登录")
            return {
                "is_logged_in": True,
                "message": "当前会话有效，无需重新登录",
                "login_time": login_time_str
            }
        else:
            # 检查是否已经有登录任务在进行中
            if login_in_progress:
                logger.info("登录任务正在后台进行中")
                return {
                    "is_logged_in": False,
                    "message": "登录任务正在后台进行中，请稍后再试",
                    "login_time": None
                }

            # 会话无效，在后台执行登录
            logger.info("当前会话无效，将在后台执行登录")
            login_in_progress = True
            background_tasks.add_task(background_login)

            return {
                "is_logged_in": False,
                "message": "登录将在后台执行，请稍后再次检查登录状态",
                "login_time": None
            }

    except Exception as e:
        logger.error(f"检查登录状态时发生错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"检查登录状态失败: {str(e)}")
