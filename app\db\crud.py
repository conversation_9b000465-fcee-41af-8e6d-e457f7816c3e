from datetime import datetime
from typing import List, Dict, Any, Optional

from app.db.database import db_cursor
from app.core.logging_config import get_db_logger

logger = get_db_logger()

def get_school_info(school_id: int) -> Optional[Dict[str, Any]]:
    """
    获取学校信息

    Args:
        school_id: 学校ID

    Returns:
        Dict[str, Any] or None: 学校信息字典，如果不存在则返回None
    """
    try:
        with db_cursor() as cursor:
            sql = "SELECT id, detail_url, detail_url_status FROM ba_school_info WHERE id = %s"
            cursor.execute(sql, (school_id,))
            result = cursor.fetchone()

            if result:
                logger.info(f"获取学校信息成功: ID={school_id}")
            else:
                logger.warning(f"学校信息不存在: ID={school_id}")

            return result
    except Exception as e:
        logger.error(f"获取学校信息失败: ID={school_id}, 错误={str(e)}")
        raise

def update_crawl_start_time(school_id: int) -> bool:
    """
    更新学校爬取开始时间

    Args:
        school_id: 学校ID

    Returns:
        bool: 更新是否成功
    """
    try:
        with db_cursor() as cursor:
            sql = "UPDATE ba_school_info SET crawl_start_time = NOW() WHERE id = %s"
            result = cursor.execute(sql, (school_id,))

            if result:
                logger.info(f"更新学校爬取开始时间成功: ID={school_id}")
                return True
            else:
                logger.warning(f"更新学校爬取开始时间失败: ID={school_id}")
                return False
    except Exception as e:
        logger.error(f"更新学校爬取开始时间出错: ID={school_id}, 错误={str(e)}")
        raise

def update_crawl_end_time(school_id: int) -> bool:
    """
    更新学校爬取结束时间和状态

    Args:
        school_id: 学校ID

    Returns:
        bool: 更新是否成功
    """
    try:
        with db_cursor() as cursor:
            sql = "UPDATE ba_school_info SET detail_url_status = 1, crawl_end_time = NOW() WHERE id = %s"
            result = cursor.execute(sql, (school_id,))

            if result:
                logger.info(f"更新学校爬取结束时间和状态成功: ID={school_id}")
                return True
            else:
                logger.warning(f"更新学校爬取结束时间和状态失败: ID={school_id}")
                return False
    except Exception as e:
        logger.error(f"更新学校爬取结束时间和状态出错: ID={school_id}, 错误={str(e)}")
        raise

def save_admission_list_append(data: List[Dict[str, Any]], school_id: int, page_num: int, grid_view_id: str = None, should_cleanup: bool = False) -> bool:
    """
    追加保存录取名单数据（用于分页数据）

    Args:
        data: 录取名单数据列表
        school_id: 学校ID
        page_num: 页码
        grid_view_id: GridView ID（用于区分不同年份）
        should_cleanup: 是否需要清理旧数据

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有录取名单数据需要保存: ID={school_id}, 页码={page_num}, GridView={grid_view_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 根据should_cleanup参数决定是否清理旧数据
            # if should_cleanup:
            #     delete_sql = "DELETE FROM ba_admission_list WHERE school_id = %s"
            #     cursor.execute(delete_sql, (school_id,))
            #     logger.info(f"删除学校旧的录取名单数据: ID={school_id}")

            # 插入新数据
            for item in data:
                logger.debug(f"保存学校录取名单 {grid_view_id} 第{page_num}页: ID={school_id}, 姓名={item['name']}")
                sql = """
                INSERT INTO ba_admission_list
                (school_id, year, name, college, major_code, major_name, initial_score,
                retest_score, total_score, first_choice_school, student_remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['initial_score'],
                    item['retest_score'], item['total_score'], item['first_choice_school'],
                    item['student_remark']
                ))

            logger.info(f"保存录取名单数据成功: ID={school_id}, {grid_view_id} 第{page_num}页, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存录取名单数据失败: ID={school_id}, {grid_view_id} 第{page_num}页, 错误={str(e)}")
        raise

def save_retest_list_append(data: List[Dict[str, Any]], school_id: int, page_num: int, grid_view_id: str = None, should_cleanup: bool = False) -> bool:
    """
    追加保存复试名单数据（用于分页数据）

    Args:
        data: 复试名单数据列表
        school_id: 学校ID
        page_num: 页码
        grid_view_id: GridView ID（用于区分不同年份）
        should_cleanup: 是否需要清理旧数据

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有复试名单数据需要保存: ID={school_id}, 页码={page_num}, GridView={grid_view_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 根据should_cleanup参数决定是否清理旧数据
            # if should_cleanup:
            #     delete_sql = "DELETE FROM ba_retest_list WHERE school_id = %s"
            #     cursor.execute(delete_sql, (school_id,))
            #     logger.info(f"删除学校旧的复试名单数据: ID={school_id}")

            # 插入新数据
            for item in data:
                logger.debug(f"保存学校复试名单 {grid_view_id} 第{page_num}页: ID={school_id}, 姓名={item['name']}")
                sql = """
                INSERT INTO ba_retest_list
                (school_id, year, name, college, major_code, major_name, politics_score,
                english_score, major1_score, major2_score, initial_score, volunteer_type, admission_status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['politics_score'],
                    item['english_score'], item['major1_score'], item['major2_score'],
                    item['initial_score'], item['volunteer_type'], item['admission_status']
                ))

            logger.info(f"保存复试名单数据成功: ID={school_id}, {grid_view_id} 第{page_num}页, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存复试名单数据失败: ID={school_id}, {grid_view_id} 第{page_num}页, 错误={str(e)}")
        raise

def save_admission_list(data: List[Dict[str, Any]], school_id: int) -> bool:
    """
    保存录取名单数据

    Args:
        data: 录取名单数据列表
        school_id: 学校ID

    Returns:
        bool: 保存是否成功
    """
    
   
    
    if not data:
        logger.info(f"没有录取名单数据需要保存: ID={school_id}")
        return True

    try:
        with db_cursor() as cursor:
            
            # 先删除该学校的旧数据
            # delete_sql = "DELETE FROM ba_admission_list WHERE school_id = %s"
            # cursor.execute(delete_sql, (school_id,))
            # logger.info(f"删除学校旧的录取名单数据: ID={school_id}")
            
                # 插入新数据
            for item in data:
                logger.info(f"保存学校录取名单 初试分数: ID={school_id}, 分数={item['initial_score']}")
                sql = """
                INSERT INTO ba_admission_list
                (school_id, year, name, college, major_code, major_name, initial_score,
                retest_score, total_score, first_choice_school, student_remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['initial_score'],
                    item['retest_score'], item['total_score'], item['first_choice_school'],
                    item['student_remark']
                ))
           
            logger.info(f"保存学校录取名单数据成功: ID={school_id}, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存学校录取名单数据失败: ID={school_id}, 错误={str(e)}")
        raise

def save_retest_list(data: List[Dict[str, Any]], school_id: int) -> bool:
    """
    保存复试名单数据

    Args:
        data: 复试名单数据列表
        school_id: 学校ID

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有复试名单数据需要保存: ID={school_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 先删除该学校的旧数据
            # delete_sql = "DELETE FROM ba_retest_list WHERE school_id = %s"
            # cursor.execute(delete_sql, (school_id,))
            # logger.info(f"删除学校旧的复试名单数据: ID={school_id}")

            # 插入新数据
            for item in data:
                sql = """
                INSERT INTO ba_retest_list
                (school_id, year, name, college, major_code, major_name, politics_score,
                english_score, major1_score, major2_score, initial_score, volunteer_type, admission_status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['politics_score'],
                    item['english_score'], item['major1_score'], item['major2_score'],
                    item['initial_score'], item['volunteer_type'], item['admission_status']
                ))

            logger.info(f"保存学校复试名单数据成功: ID={school_id}, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存学校复试名单数据失败: ID={school_id}, 错误={str(e)}")
        raise

def save_school_basic_info(data: Dict[str, Any]) -> bool:
    """
    保存学校基本信息

    Args:
        data: 学校基本信息数据

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有学校基本信息数据需要保存")
        return True

    try:
        with db_cursor() as cursor:
            # 检查是否已存在记录
            check_sql = "SELECT id FROM ba_school_basic_info WHERE school_id = %s"
            cursor.execute(check_sql, (data['school_id'],))
            result = cursor.fetchone()

            if result:
                # 更新现有记录
                sql = """
                UPDATE ba_school_basic_info
                SET research_direction = %s, exam_range = %s, reference_books = %s, retest_content = %s,
                tuition_fee = %s, study_years = %s, accommodation = %s, admission_requirements = %s, update_time = NOW()
                WHERE school_id = %s
                """
                cursor.execute(sql, (
                    data['research_direction'], data['exam_range'],
                    data['reference_books'], data['retest_content'],
                    data['tuition_fee'], data['study_years'],
                    data['accommodation'], data['admission_requirements'],
                    data['school_id']
                ))
                logger.info(f"更新学校基本信息成功: ID={data['school_id']}")
                logger.info(f"data: {data['exam_range']}")
            else:
                # 插入新记录
                sql = """
                INSERT INTO ba_school_basic_info
                (school_id, research_direction, exam_range, reference_books, retest_content,
                tuition_fee, study_years, accommodation, admission_requirements)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    data['school_id'], data['research_direction'],
                    data['exam_range'], data['reference_books'],
                    data['retest_content'], data['tuition_fee'],
                    data['study_years'], data['accommodation'],
                    data['admission_requirements']
                ))
                logger.info(f"插入学校基本信息成功: ID={data['school_id']}")

            return True
    except Exception as e:
        logger.error(f"保存学校基本信息失败: ID={data['school_id']}, 错误={str(e)}")
        raise

def save_transfer_info(data: List[Dict[str, Any]], school_id: int) -> bool:
    """
    保存调剂信息数据

    Args:
        data: 调剂信息数据列表
        school_id: 学校ID

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有调剂信息数据需要保存: ID={school_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 先删除该学校的旧数据
            delete_sql = "DELETE FROM ba_transfer_info WHERE school_id = %s"
            cursor.execute(delete_sql, (school_id,))
            logger.info(f"删除学校旧的调剂信息数据: ID={school_id}")

            # 插入新数据
            for item in data:
                sql = """
                INSERT INTO ba_transfer_info
                (school_id, year, name, transfer_school, college, major_code,
                major_name, initial_score, apply_school, apply_major)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['transfer_school'],
                    item['college'], item['major_code'], item['major_name'],
                    item['initial_score'], item['apply_school'], item['apply_major']
                ))

            logger.info(f"保存学校调剂信息数据成功: ID={school_id}, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存学校调剂信息数据失败: ID={school_id}, 错误={str(e)}")
        raise

def save_admission_list_backup(data: List[Dict[str, Any]], school_id: int) -> bool:
    """
    保存录取名单数据到备份表

    Args:
        data: 录取名单数据列表
        school_id: 学校ID

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有录取名单数据需要保存到备份表: ID={school_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 先删除该学校的旧数据
            delete_sql = "DELETE FROM ba_admission_list_backup WHERE school_id = %s"
            cursor.execute(delete_sql, (school_id,))
            logger.info(f"删除学校旧的录取名单备份数据: ID={school_id}")

            # 插入新数据到备份表
            for item in data:
                sql = """
                INSERT INTO ba_admission_list_backup
                (school_id, year, name, college, major_code, major_name, initial_score,
                retest_score, total_score, first_choice_school, student_remark)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['initial_score'],
                    item['retest_score'], item['total_score'], item['first_choice_school'],
                    item['student_remark']
                ))

            logger.info(f"保存学校录取名单数据到备份表成功: ID={school_id}, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存学校录取名单数据到备份表失败: ID={school_id}, 错误={str(e)}")
        raise

def save_retest_list_backup(data: List[Dict[str, Any]], school_id: int) -> bool:
    """
    保存复试名单数据到备份表

    Args:
        data: 复试名单数据列表
        school_id: 学校ID

    Returns:
        bool: 保存是否成功
    """
    if not data:
        logger.info(f"没有复试名单数据需要保存到备份表: ID={school_id}")
        return True

    try:
        with db_cursor() as cursor:
            # 先删除该学校的旧数据
            delete_sql = "DELETE FROM ba_retest_list_backup WHERE school_id = %s"
            cursor.execute(delete_sql, (school_id,))
            logger.info(f"删除学校旧的复试名单备份数据: ID={school_id}")

            # 插入新数据到备份表
            for item in data:
                sql = """
                INSERT INTO ba_retest_list_backup
                (school_id, year, name, college, major_code, major_name, politics_score,
                english_score, major1_score, major2_score, initial_score, volunteer_type, admission_status)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(sql, (
                    item['school_id'], item['year'], item['name'], item['college'],
                    item['major_code'], item['major_name'], item['politics_score'],
                    item['english_score'], item['major1_score'], item['major2_score'],
                    item['initial_score'], item['volunteer_type'], item['admission_status']
                ))

            logger.info(f"保存学校复试名单数据到备份表成功: ID={school_id}, 数量={len(data)}")
            return True
    except Exception as e:
        logger.error(f"保存学校复试名单数据到备份表失败: ID={school_id}, 错误={str(e)}")
        raise
