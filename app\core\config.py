from typing import Dict, Any, Union
from dotenv import load_dotenv
import os
from pathlib import Path
load_dotenv()
class Settings:
    """应用程序配置类"""

    # 项目基本信息
    PROJECT_NAME: str = "学校信息爬取API服务"
    PROJECT_VERSION: str = "1.0.0"
    API_PREFIX: str = "/api"

    # 线程池最大线程数
    MAX_WORKERS: int = 5

    # 腾讯云OCR配置
    SECRET_ID: str = "AKIDb5QbXEk11kCTWKsAKYWG0avAD3f5GI9m"
    SECRET_KEY: str = "UfwykKyhFZfW7UhfS0IyRr7A6rMdvEPN"

    # 登录账号配置
    USERNAME: str = "yanquzxm"
    PASSWORD: str = "yanqu0501"

    # Redis配置
    REDIS: Dict[str, Union[str, int]] = {
        "host": "localhost",
        "port": 6379,
        "db": 0
    }

    # MySQL配置
    MYSQL: Dict[str, Any] = {
        "host": os.getenv("MYSQL_HOST", "localhost"),
        "port": int(os.getenv("MYSQL_PORT", 3306)),
        #"user": "ai_select",
        #"password": "arC4KtecFze4Hn7i",
        "user": os.getenv("MYSQL_USER","ai_select"),
        "password": os.getenv("MYSQL_PASSWORD","arC4KtecFze4Hn7i"),
        "db": os.getenv("MYSQL_DATABASE","ai_select"),
        "charset": "utf8mb4"
    }

    # 爬虫配置
    CRAWLER: Dict[str, Any] = {
        "login_url": "http://www.zhiliaojy.com/login.aspx",
        "test_url": "http://www.zhiliaojy.com/admin/zxxiangqing.aspx?dataid=AaooQgbFRnSJHkeIyO56yJoGGyeuOF",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",
        "login_timeout": 60*60*6,  # 登录会话有效期（秒）
        "retry_count": 3,  # 登录重试次数
        "retry_interval": 5,  # 登录重试间隔（秒）
    }

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_CONFIG: Dict[str, Any] = {
        "max_bytes": 10485760,  # 10MB
        "backup_count": 20,     # 保留20个备份
        "when": "midnight",     # 每天午夜切换日志文件
        "interval": 1,          # 每1个时间单位切换一次
        "encoding": "utf-8"
    }

    # 文件路径
    BASE_DIR: Path = Path(__file__).resolve().parent.parent.parent
    LOGS_DIR: Path = BASE_DIR / "logs"

    def __init__(self):
        """初始化时确保日志目录存在"""
        os.makedirs(self.LOGS_DIR, exist_ok=True)

# 创建全局设置实例



settings = Settings()
