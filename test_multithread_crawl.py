#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多线程直接跳转爬取功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_limited_sync_crawl():
    """测试限制版本的同步爬取接口（多线程直接跳转）"""
    
    # 测试数据
    test_data = {
        "ids": [1214]  # 测试一个学校ID
    }
    
    print("=" * 60)
    print("测试限制版本同步爬取接口（多线程直接跳转）")
    print("=" * 60)
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        print(f"发送请求到: {BASE_URL}/crawl_school_info_sync_limited")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=300  # 5分钟超时
        )
        
        # 记录结束时间
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"响应状态码: {response.status_code}")
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
        print(f"总耗时: {elapsed_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print("\n响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 分析结果
            if "results" in result:
                results = result["results"]
                success_count = sum(1 for r in results if r.get("status") == "success")
                skipped_count = sum(1 for r in results if r.get("status") == "skipped")
                error_count = sum(1 for r in results if r.get("status") == "error")
                
                print(f"\n结果统计:")
                print(f"成功: {success_count}")
                print(f"跳过: {skipped_count}")
                print(f"失败: {error_count}")
                
                # 显示详细结果
                for i, result_item in enumerate(results):
                    print(f"\n学校 {i+1} (ID: {result_item.get('school_id')}):")
                    print(f"  状态: {result_item.get('status')}")
                    print(f"  消息: {result_item.get('message')}")
                    if result_item.get('data_summary'):
                        print(f"  数据摘要: {result_item.get('data_summary')}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("请求超时")
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保服务器正在运行")
    except Exception as e:
        print(f"发生错误: {str(e)}")

def check_backup_tables():
    """检查备份表中的数据"""
    print("\n" + "=" * 60)
    print("检查备份表数据")
    print("=" * 60)
    
    # 这里可以添加数据库查询逻辑来检查备份表中的数据
    # 由于没有直接的数据库查询API，这里只是一个占位符
    print("请手动检查数据库中的 ba_admission_list_backup 和 ba_retest_list_backup 表")
    print("查看是否有新增的当年数据（第一页和最后一页）")

def test_performance_comparison():
    """性能对比测试"""
    print("\n" + "=" * 60)
    print("性能对比测试")
    print("=" * 60)
    
    test_data = {
        "ids": [1214]
    }
    
    # 测试原版API
    print("测试原版同步接口...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync",
            json=test_data,
            timeout=300
        )
        end_time = time.time()
        original_time = end_time - start_time
        
        print(f"原版API耗时: {original_time:.2f} 秒")
        print(f"原版API状态码: {response.status_code}")
        
    except Exception as e:
        print(f"原版API错误: {str(e)}")
        original_time = None
    
    print("\n" + "-" * 40)
    
    # 测试限制版本API
    print("测试限制版本同步接口...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=300
        )
        end_time = time.time()
        limited_time = end_time - start_time
        
        print(f"限制版本API耗时: {limited_time:.2f} 秒")
        print(f"限制版本API状态码: {response.status_code}")
        
        # 计算性能提升
        if original_time and limited_time:
            improvement = ((original_time - limited_time) / original_time) * 100
            print(f"\n性能提升: {improvement:.1f}%")
            print(f"时间节省: {original_time - limited_time:.2f} 秒")
        
    except Exception as e:
        print(f"限制版本API错误: {str(e)}")

if __name__ == "__main__":
    print("开始测试多线程直接跳转爬取功能...")
    
    # 测试限制版本的同步爬取
    test_limited_sync_crawl()
    
    # 检查备份表数据
    check_backup_tables()
    
    # 性能对比测试
    test_performance_comparison()
    
    print("\n测试完成!")
    print("\n注意事项:")
    print("1. 新版本使用多线程同时爬取第一页和最后一页")
    print("2. 数据保存到备份表 ba_admission_list_backup 和 ba_retest_list_backup")
    print("3. 只爬取当年的拟录取名单和复试名单")
    print("4. 理论上应该比原版本更快，因为减少了页面跳转次数")
