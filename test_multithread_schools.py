#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多线程并发爬取多个学校的功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_multithread_limited_crawl():
    """测试多线程限制版本的同步爬取接口"""
    
    # 测试数据 - 多个学校ID
    test_data = {
        "ids": [19402, 19403,19404,19405,19406,19407,19408,19409,19410]  # 测试多个学校ID
    }

    resp = requests.post(f"{BASE_URL}/crawl_school_info_sync_limited", json=test_data)
    print(resp.json())
if __name__ == "__main__":
    test_multithread_limited_crawl()
