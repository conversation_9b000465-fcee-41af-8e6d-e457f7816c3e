#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试多线程并发爬取多个学校的功能
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_multithread_limited_crawl():
    """测试多线程限制版本的同步爬取接口"""
    
    # 测试数据 - 多个学校ID
    test_data = {
        "ids": [19402, 19403]  # 测试多个学校ID
    }
    
    print("=" * 80)
    print("测试多线程限制版本同步爬取接口（每个学校ID独立线程）")
    print("=" * 80)
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        print(f"发送请求到: {BASE_URL}/crawl_school_info_sync_limited")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        print(f"学校数量: {len(test_data['ids'])}")
        
        print("\n多线程功能说明:")
        print("- 每个学校ID使用独立线程并发爬取")
        print("- 最多5个并发线程，避免过多并发")
        print("- 每个线程内部使用尾页按钮直接跳转")
        print("- 线程安全的结果收集")
        print("- 只爬取当年数据，保存到备份表")
        
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=600  # 10分钟超时，因为是多个学校
        )
        
        # 记录结束时间
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
        print(f"总耗时: {elapsed_time:.2f} 秒")
        print(f"平均每个学校耗时: {elapsed_time/len(test_data['ids']):.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print("\n响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 分析结果
            if "results" in result:
                results = result["results"]
                success_count = sum(1 for r in results if r.get("status") == "success")
                skipped_count = sum(1 for r in results if r.get("status") == "skipped")
                error_count = sum(1 for r in results if r.get("status") == "error")
                
                print(f"\n结果统计:")
                print(f"总数: {len(results)}")
                print(f"成功: {success_count}")
                print(f"跳过: {skipped_count}")
                print(f"失败: {error_count}")
                print(f"成功率: {(success_count/len(results)*100):.1f}%")
                
                # 显示详细结果
                for i, result_item in enumerate(results):
                    print(f"\n学校 {i+1} (ID: {result_item.get('school_id')}):")
                    print(f"  状态: {result_item.get('status')}")
                    print(f"  消息: {result_item.get('message')}")
                    if result_item.get('data_summary'):
                        print(f"  数据摘要: {result_item.get('data_summary')}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("请求超时")
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保服务器正在运行")
    except Exception as e:
        print(f"发生错误: {str(e)}")

def test_single_vs_multithread():
    """对比单线程和多线程的性能"""
    print("\n" + "=" * 80)
    print("单线程 vs 多线程性能对比")
    print("=" * 80)
    
    test_data = {
        "ids": [19402, 19403]
    }
    
    # 测试原版API（单线程串行）
    print("测试原版同步接口（单线程串行）...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync",
            json=test_data,
            timeout=600
        )
        end_time = time.time()
        single_thread_time = end_time - start_time
        
        print(f"单线程耗时: {single_thread_time:.2f} 秒")
        print(f"单线程状态码: {response.status_code}")
        
    except Exception as e:
        print(f"单线程API错误: {str(e)}")
        single_thread_time = None
    
    print("\n" + "-" * 50)
    
    # 测试多线程版本API
    print("测试多线程限制版本接口...")
    try:
        start_time = time.time()
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=600
        )
        end_time = time.time()
        multithread_time = end_time - start_time
        
        print(f"多线程耗时: {multithread_time:.2f} 秒")
        print(f"多线程状态码: {response.status_code}")
        
        # 计算性能提升
        if single_thread_time and multithread_time:
            if multithread_time < single_thread_time:
                improvement = ((single_thread_time - multithread_time) / single_thread_time) * 100
                print(f"\n性能提升: {improvement:.1f}%")
                print(f"时间节省: {single_thread_time - multithread_time:.2f} 秒")
            else:
                print(f"\n多线程耗时更长，可能是因为线程开销或其他因素")
        
    except Exception as e:
        print(f"多线程API错误: {str(e)}")

def show_multithread_info():
    """显示多线程功能说明"""
    print("\n" + "=" * 80)
    print("多线程并发爬取功能说明")
    print("=" * 80)
    print("1. 并发策略:")
    print("   - 每个学校ID分配一个独立线程")
    print("   - 最多5个并发线程，避免过载")
    print("   - 线程安全的结果收集机制")
    print()
    print("2. 单个学校处理流程:")
    print("   - 检查学校信息和爬取状态")
    print("   - 获取第一页内容和总页数")
    print("   - 多线程处理第一页和最后一页")
    print("   - 使用尾页按钮直接跳转到最后一页")
    print("   - 保存数据到备份表（先删除旧数据）")
    print()
    print("3. 优势:")
    print("   - 多个学校并行处理，总体时间大幅缩短")
    print("   - 单个学校内部也使用多线程（第一页+最后一页）")
    print("   - 线程安全，避免数据竞争")
    print("   - 错误隔离，一个学校失败不影响其他学校")
    print()
    print("4. 适用场景:")
    print("   - 批量爬取多个学校数据")
    print("   - 需要快速获取当年最新数据")
    print("   - 定期数据更新任务")

if __name__ == "__main__":
    print("开始测试多线程并发爬取功能...")
    
    # 显示功能说明
    show_multithread_info()
    
    # 测试多线程限制版本的同步爬取
    test_multithread_limited_crawl()
    
    # 性能对比测试
    test_single_vs_multithread()
    
    print("\n测试完成!")
    print("\n注意事项:")
    print("1. 多线程版本理论上应该比单线程版本更快")
    print("2. 检查数据库备份表中是否有所有学校的数据")
    print("3. 观察日志中的线程处理信息")
    print("4. 如果学校数量很多，建议分批处理")
