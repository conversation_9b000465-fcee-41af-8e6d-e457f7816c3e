import concurrent.futures
import threading
import time
from datetime import datetime
def work(i:int):
    print(f"[{threading.current_thread().name}] start work {i}")
    time.sleep(1)
    print(f"[{threading.current_thread().name}] end work {i}")
    return i*i
if __name__ =="__main__":
    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(work, i) for i in range(10)]
        for future in concurrent.futures.as_completed(futures):
            print(future.result()) 