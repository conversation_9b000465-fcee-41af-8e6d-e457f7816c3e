import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from app.api.router import api_router
from app.core.config import settings
from app.core.logging_config import setup_logging
from app.services.background_tasks import start_worker, stop_worker

# 设置日志
log_level = getattr(logging, settings.LOG_LEVEL, logging.INFO)
logger = setup_logging(log_level)

# 定义生命周期管理器
@asynccontextmanager
async def lifespan(app_context: FastAPI):
    # 启动时执行
    logger.info("应用启动")
    # 启动后台工作线程
    start_worker()
    logger.info("后台工作线程已启动")

    yield  # 应用运行期间

    # 关闭时执行
    logger.info("应用关闭")
    # 停止后台工作线程
    stop_worker()
    logger.info("后台工作线程已停止")

# 创建FastAPI应用
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.PROJECT_VERSION,
    description="爬取学校详细信息并更新数据库的API服务",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(api_router)

@app.get("/")
async def root():
    """根路径，返回欢迎信息"""
    return {
        "message": f"欢迎使用{settings.PROJECT_NAME}",
        "version": settings.PROJECT_VERSION,
        "docs_url": "/docs"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "ok"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=False)  # 关闭自动重载，避免多次启动工作线程
