import threading
import time
from typing import List, Dict, Any, Optional
import queue

from app.services.processor import process_school_info
from app.core.logging_config import get_logger

logger = get_logger("background")

# 全局任务队列
task_queue = queue.Queue()

# 任务结果存储
task_results = {}

# 任务状态
TASK_STATUS = {
    "PENDING": "pending",
    "RUNNING": "running",
    "COMPLETED": "completed",
    "FAILED": "failed"
}

class TaskManager:
    """任务管理器，负责创建和管理后台任务"""
    
    @staticmethod
    def create_task(task_id: str, school_ids: List[int]) -> Dict[str, Any]:
        """
        创建一个新任务
        
        Args:
            task_id: 任务ID
            school_ids: 学校ID列表
            
        Returns:
            Dict[str, Any]: 任务信息
        """
        # 创建任务信息
        task_info = {
            "task_id": task_id,
            "status": TASK_STATUS["PENDING"],
            "school_ids": school_ids,
            "total": len(school_ids),
            "processed": 0,
            "success": 0,
            "skipped": 0,
            "error": 0,
            "results": [],
            "start_time": time.time(),
            "end_time": None
        }
        
        # 存储任务信息
        task_results[task_id] = task_info
        
        # 将任务添加到队列
        task_queue.put((task_id, school_ids))
        
        logger.info(f"创建任务: ID={task_id}, 学校数量={len(school_ids)}")
        
        return task_info
    
    @staticmethod
    def get_task_status(task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            Dict[str, Any] or None: 任务信息，如果不存在则返回None
        """
        return task_results.get(task_id)
    
    @staticmethod
    def get_all_tasks() -> Dict[str, Dict[str, Any]]:
        """
        获取所有任务
        
        Returns:
            Dict[str, Dict[str, Any]]: 所有任务信息
        """
        return task_results

def worker():
    """
    工作线程，从队列中获取任务并处理
    """
    logger.info("启动后台工作线程")
    
    while True:
        try:
            # 从队列中获取任务
            task_id, school_ids = task_queue.get()
            
            if task_id is None:
                # 收到退出信号
                logger.info("工作线程收到退出信号")
                break
            
            # 更新任务状态
            task_info = task_results[task_id]
            task_info["status"] = TASK_STATUS["RUNNING"]
            
            logger.info(f"开始处理任务: ID={task_id}, 学校数量={len(school_ids)}")
            
            # 处理每个学校
            for school_id in school_ids:
                try:
                    # 处理学校信息
                    success, result = process_school_info(school_id)
                    
                    # 更新任务信息
                    task_info["processed"] += 1
                    task_info["results"].append(result)
                    
                    if result["status"] == "success":
                        task_info["success"] += 1
                    elif result["status"] == "skipped":
                        task_info["skipped"] += 1
                    else:
                        task_info["error"] += 1
                    
                except Exception as e:
                    logger.error(f"处理学校信息时发生错误: ID={school_id}, 错误={str(e)}")
                    
                    # 更新任务信息
                    task_info["processed"] += 1
                    task_info["error"] += 1
                    task_info["results"].append({
                        "school_id": school_id,
                        "status": "error",
                        "message": f"处理失败: {str(e)}",
                        "data_summary": None
                    })
            
            # 更新任务状态
            task_info["status"] = TASK_STATUS["COMPLETED"]
            task_info["end_time"] = time.time()
            
            logger.info(f"任务处理完成: ID={task_id}, 总数={task_info['total']}, 成功={task_info['success']}, 跳过={task_info['skipped']}, 失败={task_info['error']}")
            
            # 标记任务完成
            task_queue.task_done()
            
        except Exception as e:
            logger.error(f"工作线程处理任务时发生错误: {str(e)}")
            
            # 如果有任务ID，更新任务状态
            if 'task_id' in locals() and task_id in task_results:
                task_results[task_id]["status"] = TASK_STATUS["FAILED"]
                task_results[task_id]["end_time"] = time.time()
            
            # 标记任务完成
            if 'task_queue' in locals() and hasattr(task_queue, 'task_done'):
                task_queue.task_done()

# 工作线程
worker_thread = None

def start_worker():
    """启动工作线程"""
    global worker_thread
    
    if worker_thread is None or not worker_thread.is_alive():
        worker_thread = threading.Thread(target=worker, daemon=True)
        worker_thread.start()
        logger.info("后台工作线程已启动")

def stop_worker():
    """停止工作线程"""
    global worker_thread
    
    if worker_thread and worker_thread.is_alive():
        # 发送退出信号
        task_queue.put((None, None))
        
        # 等待线程结束
        worker_thread.join(timeout=5)
        
        if worker_thread.is_alive():
            logger.warning("工作线程未能正常退出")
        else:
            logger.info("工作线程已停止")
            worker_thread = None
