from typing import Dict, Any, List, Tuple

from app.core.logging_config import get_logger
from app.db import crud
from app.services.crawler import fetch_page
from app.services.parser import parse_page_data
import json

logger = get_logger("processor")

def process_school_info(school_id: int) -> Tuple[bool, Dict[str, Any]]:
    """
    处理单个学校信息
    
    Args:
        school_id: 学校ID
        
    Returns:
        Tuple[bool, Dict[str, Any]]: 处理是否成功，以及处理结果
    """
    result = {
        "school_id": school_id,
        "status": "error",
        "message": "处理失败",
        "data_summary": None
    }
    
    try:
        logger.info(f"开始处理学校信息: ID={school_id}")
        
        # 获取学校信息
        school_info = crud.get_school_info(school_id)
        
        if not school_info:
            logger.warning(f"学校信息不存在: ID={school_id}")
            result["message"] = "学校信息不存在"
            return False, result
        
        # 如果已经爬取过，跳过
        if school_info['detail_url_status'] == 1:
            logger.info(f"学校信息已爬取，跳过: ID={school_id}")
            result["status"] = "skipped"
            result["message"] = "该学校信息已爬取"
            return True, result
        
        # 如果没有URL，标记为错误
        if not school_info['detail_url']:
            logger.warning(f"学校详情URL为空: ID={school_id}")
            result["message"] = "学校详情URL为空"
            return False, result
        
        # 更新爬取开始时间
        crud.update_crawl_start_time(school_id)
        
        # 爬取详情页（传递school_id以支持逐页保存）
        success, html_content = fetch_page(school_info['detail_url'], school_id)
        
        if not success or not html_content:
            logger.error(f"爬取学校详情页失败: ID={school_id}")
            result["message"] = "爬取学校详情页失败"
            return False, result
        
        # 解析数据
        data = parse_page_data(html_content, school_id)
        logger.info(f"数据解析完成: {json.dumps(data, ensure_ascii=False)}")

        # 保存数据到数据库
        try:
            # 保存录取名单
            crud.save_admission_list(data['admission_list'], school_id)
            
            # 保存复试名单
            crud.save_retest_list(data['retest_list'], school_id)
            
            # 保存学校基本信息
            crud.save_school_basic_info(data['basic_info'])
            
            # 保存调剂信息
            crud.save_transfer_info(data['transfer_info'], school_id)
            
            # 更新学校信息表的状态
            crud.update_crawl_end_time(school_id)
            
            logger.info(f"数据保存成功: ID={school_id}")
            
            # 更新结果
            result["status"] = "success"
            result["message"] = "数据爬取成功"
            result["data_summary"] = {
                "admission_count": len(data['admission_list']),
                "retest_count": len(data['retest_list']),
                "transfer_count": len(data['transfer_info']),
                "has_basic_info": bool(data['basic_info'])
            }
            
            return True, result
        
        except Exception as e:
            logger.error(f"保存数据失败: ID={school_id}, 错误={str(e)}")
            result["message"] = f"保存数据失败: {str(e)}"
            return False, result
    
    except Exception as e:
        logger.error(f"处理学校信息时发生未捕获的错误: ID={school_id}, 错误={str(e)}")
        result["message"] = f"处理失败: {str(e)}"
        return False, result

def process_school_info_batch(school_ids: List[int]) -> List[Dict[str, Any]]:
    """
    批量处理学校信息
    
    Args:
        school_ids: 学校ID列表
        
    Returns:
        List[Dict[str, Any]]: 处理结果列表
    """
    results = []
    
    logger.info(f"开始批量处理学校信息: IDs={school_ids}")
    
    for school_id in school_ids:
        _, result = process_school_info(school_id)
        results.append(result)
    
    logger.info(f"批量处理学校信息完成: 共{len(school_ids)}个学校")

    return results

def process_school_info_limited(school_id: int) -> Tuple[bool, Dict[str, Any]]:
    """
    处理单个学校信息（限制版本：只爬取当年数据，只爬第一页和最后一页，保存到备份表）

    Args:
        school_id: 学校ID

    Returns:
        Tuple[bool, Dict[str, Any]]: 处理是否成功，以及结果信息
    """
    result = {
        "school_id": school_id,
        "status": "error",
        "message": "",
        "data_summary": None
    }

    try:
        logger.info(f"开始处理学校信息（限制版本）: ID={school_id}")

        # 获取学校信息
        school_info = crud.get_school_info(school_id)
        if not school_info:
            result["status"] = "error"
            result["message"] = "学校信息不存在"
            logger.warning(f"学校信息不存在: ID={school_id}")
            return False, result

        # 检查是否已经爬取过
        if school_info.get('detail_url_status') == 1:
            result["status"] = "skipped"
            result["message"] = "学校信息已爬取，跳过处理"
            logger.info(f"学校信息已爬取，跳过处理: ID={school_id}")
            return True, result

        # 更新爬取开始时间
        crud.update_crawl_start_time(school_id)

        # 获取详情页URL
        detail_url = school_info.get('detail_url')
        if not detail_url:
            result["message"] = "学校详情页URL为空"
            logger.error(f"学校详情页URL为空: ID={school_id}")
            return False, result

        logger.info(f"开始爬取学校详情页（限制版本）: ID={school_id}, URL={detail_url}")

        # 爬取页面内容（限制版本）
        from app.services.crawler import fetch_page_with_pagination_limited
        success, html_content = fetch_page_with_pagination_limited(detail_url, school_id)

        if not success or not html_content:
            result["message"] = "爬取页面失败"
            logger.error(f"爬取页面失败: ID={school_id}")
            return False, result

        logger.info(f"页面爬取成功（限制版本）: ID={school_id}")

        # 解析页面内容（只解析基本信息和调剂信息，拟录取和复试名单已在爬取过程中保存到备份表）
        from app.services.parser import parse_page_data
        data = parse_page_data(html_content, school_id)

        logger.info(f"页面解析完成（限制版本）: ID={school_id}")

        # 保存数据到数据库（只保存基本信息和调剂信息）
        try:
            # 保存学校基本信息
            if data.get('basic_info'):
                crud.save_school_basic_info(data['basic_info'])

            # 保存调剂信息
            if data.get('transfer_info'):
                crud.save_transfer_info(data['transfer_info'], school_id)

            # 更新学校信息表的状态
            crud.update_crawl_end_time(school_id)

            logger.info(f"数据保存成功（限制版本）: ID={school_id}")

            # 更新结果
            result["status"] = "success"
            result["message"] = "数据爬取成功（限制版本：当年数据，第一页和最后一页）"
            result["data_summary"] = {
                "admission_count": "已保存到备份表",
                "retest_count": "已保存到备份表",
                "transfer_count": len(data.get('transfer_info', [])),
                "has_basic_info": bool(data.get('basic_info'))
            }

            return True, result

        except Exception as e:
            logger.error(f"保存数据失败（限制版本）: ID={school_id}, 错误={str(e)}")
            result["message"] = f"保存数据失败: {str(e)}"
            return False, result

    except Exception as e:
        logger.error(f"处理学校信息时发生未捕获的错误（限制版本）: ID={school_id}, 错误={str(e)}")
        result["message"] = f"处理失败: {str(e)}"
        return False, result

def process_school_info_batch_limited(school_ids: List[int]) -> List[Dict[str, Any]]:
    """
    批量处理学校信息（限制版本）

    Args:
        school_ids: 学校ID列表

    Returns:
        List[Dict[str, Any]]: 处理结果列表
    """
    results = []

    logger.info(f"开始批量处理学校信息（限制版本）: IDs={school_ids}")

    for school_id in school_ids:
        _, result = process_school_info_limited(school_id)
        results.append(result)

    logger.info(f"批量处理学校信息完成（限制版本）: 共{len(school_ids)}个学校")

    return results
