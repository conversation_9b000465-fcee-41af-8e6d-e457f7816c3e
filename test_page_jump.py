#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试页面跳转功能的爬取API
"""

import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8000/api"

def test_limited_sync_crawl():
    """测试限制版本的同步爬取接口（使用页面跳转功能）"""
    
    # 测试数据
    test_data = {
        "ids": [1214]  # 测试一个学校ID
    }
    
    print("=" * 70)
    print("测试限制版本同步爬取接口（使用尾页按钮直接跳转到最后一页）")
    print("=" * 70)
    
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 发送请求
        print(f"发送请求到: {BASE_URL}/crawl_school_info_sync_limited")
        print(f"请求数据: {json.dumps(test_data, ensure_ascii=False)}")
        print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(start_time))}")
        print("\n功能说明:")
        print("- 优先使用尾页按钮 (GridView8_lbnLast) 直接跳转到最后一页")
        print("- 备用页面跳转功能 (GridView$ctl25$inPageNum + Button1)")
        print("- 多线程同时处理第一页和最后一页")
        print("- 只爬取当年的拟录取名单和复试名单")
        print("- 数据保存到备份表")
        
        response = requests.post(
            f"{BASE_URL}/crawl_school_info_sync_limited",
            json=test_data,
            timeout=300  # 5分钟超时
        )
        
        # 记录结束时间
        end_time = time.time()
        elapsed_time = end_time - start_time
        
        print(f"\n响应状态码: {response.status_code}")
        print(f"结束时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(end_time))}")
        print(f"总耗时: {elapsed_time:.2f} 秒")
        
        if response.status_code == 200:
            result = response.json()
            print("\n响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 分析结果
            if "results" in result:
                results = result["results"]
                success_count = sum(1 for r in results if r.get("status") == "success")
                skipped_count = sum(1 for r in results if r.get("status") == "skipped")
                error_count = sum(1 for r in results if r.get("status") == "error")
                
                print(f"\n结果统计:")
                print(f"成功: {success_count}")
                print(f"跳过: {skipped_count}")
                print(f"失败: {error_count}")
                
                # 显示详细结果
                for i, result_item in enumerate(results):
                    print(f"\n学校 {i+1} (ID: {result_item.get('school_id')}):")
                    print(f"  状态: {result_item.get('status')}")
                    print(f"  消息: {result_item.get('message')}")
                    if result_item.get('data_summary'):
                        print(f"  数据摘要: {result_item.get('data_summary')}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.Timeout:
        print("请求超时")
    except requests.exceptions.ConnectionError:
        print("连接错误，请确保服务器正在运行")
    except Exception as e:
        print(f"发生错误: {str(e)}")

def test_check_login_status():
    """测试登录状态检查"""
    print("\n" + "=" * 50)
    print("检查登录状态")
    print("=" * 50)
    
    try:
        response = requests.get(f"{BASE_URL}/check_login_status")
        print(f"响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("登录状态:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
        else:
            print(f"请求失败: {response.text}")
            
    except Exception as e:
        print(f"发生错误: {str(e)}")

def show_improvement_info():
    """显示改进信息"""
    print("\n" + "=" * 70)
    print("尾页按钮跳转功能改进说明")
    print("=" * 70)
    print("1. 原来的方式：")
    print("   - 逐页跳转：第1页 -> 第2页 -> 第3页 -> ... -> 第N页")
    print("   - 需要发送 N-1 次请求才能到达最后一页")
    print("   - 耗时较长，特别是页数很多的情况下")
    print()
    print("2. 新的方式（优先级顺序）：")
    print("   方法1：尾页按钮直接跳转")
    print("   - 使用尾页链接：<a id='GridView8_lbnLast' href='javascript:__doPostBack(...)'")
    print("   - 事件目标：GridView8$ctl25$lbnLast")
    print("   - 只需要 1 次请求直接到达最后一页")
    print()
    print("   方法2：页面跳转功能（备用）")
    print("   - GridView7$ctl25$inPageNum: 目标页码（如 27）")
    print("   - GridView7$ctl25$Button1: 跳转按钮（值为 '跳转'）")
    print("   - __EVENTTARGET: GridView7$ctl25$Button1")
    print()
    print("3. 多线程处理：")
    print("   - 第一页：直接使用已获取的HTML内容")
    print("   - 最后一页：使用尾页按钮或页面跳转功能")
    print("   - 两个页面并行处理，提高效率")
    print()
    print("4. 优势：")
    print("   - 速度最快：尾页按钮一键到达最后一页")
    print("   - 可靠性高：有备用的页面跳转方案")
    print("   - 效率更高：多线程并行处理")
    print("   - 资源节省：减少不必要的网络请求")

if __name__ == "__main__":
    print("开始测试尾页按钮跳转功能的爬取API...")

    # 显示改进信息
    show_improvement_info()

    # 检查登录状态
    test_check_login_status()

    # 等待一下
    time.sleep(2)

    # 测试限制版本的同步爬取
    test_limited_sync_crawl()

    print("\n测试完成!")
    print("\n请检查以下内容:")
    print("1. 数据库中的 ba_admission_list_backup 表是否有新数据")
    print("2. 数据库中的 ba_retest_list_backup 表是否有新数据")
    print("3. 日志中是否显示'使用尾页按钮直接跳转到最后一页'")
    print("4. 日志中是否显示成功跳转到指定页面")
    print("5. 总耗时是否比原版本更短")
