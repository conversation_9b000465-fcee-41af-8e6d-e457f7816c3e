import json
from typing import Optional
from tencentcloud.common import credential
from tencentcloud.common.profile.client_profile import ClientProfile
from tencentcloud.common.profile.http_profile import HttpProfile
from tencentcloud.common.exception.tencent_cloud_sdk_exception import TencentCloudSDKException
from tencentcloud.ocr.v20181119 import ocr_client, models

from app.core.config import settings
from app.core.logging_config import get_logger

logger = get_logger("captcha")

def recognize_captcha(image_base64: str) -> Optional[str]:
    """
    使用腾讯云OCR识别验证码
    
    Args:
        image_base64: Base64编码的验证码图片
        
    Returns:
        str or None: 识别出的验证码文本，识别失败则返回None
    """
    try:
        logger.debug("开始识别验证码")
        
        # 实例化认证对象
        cred = credential.Credential(settings.SECRET_ID, settings.SECRET_KEY)
        
        # 实例化HTTP选项
        httpProfile = HttpProfile()
        httpProfile.endpoint = "ocr.tencentcloudapi.com"
        
        # 实例化client选项
        clientProfile = ClientProfile()
        clientProfile.httpProfile = httpProfile
        
        # 实例化OCR客户端
        client = ocr_client.OcrClient(cred, "", clientProfile)
        
        # 实例化请求对象
        req = models.GeneralAccurateOCRRequest()
        params = {
            "ImageBase64": f"data:image/png;base64,{image_base64}",
        }
        req.from_json_string(json.dumps(params))
        
        # 发送请求
        resp = client.GeneralAccurateOCR(req)
        res = json.loads(resp.to_json_string())
        
        # 解析结果
        if res.get("TextDetections") is not None and len(res.get("TextDetections")) > 0:
            captcha_text = res.get("TextDetections")[0]["DetectedText"].strip()
            logger.info(f"验证码识别成功: {captcha_text}")
            return captcha_text
        else:
            logger.warning("验证码识别失败: 未检测到文本")
            return None
    
    except TencentCloudSDKException as err:
        logger.error(f"验证码识别出错: {str(err)}")
        return None
    except Exception as e:
        logger.error(f"验证码识别过程中发生未知错误: {str(e)}")
        return None
