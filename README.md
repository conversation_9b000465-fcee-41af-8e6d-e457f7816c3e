# 学校信息爬取API服务

这是一个基于FastAPI的API服务，用于爬取学校详细信息并更新数据库。

## 项目结构

```
get_detail_data/
├── app/                    # 应用程序包
│   ├── api/                # API路由和端点
│   ├── core/               # 核心配置和设置
│   ├── db/                 # 数据库连接和操作
│   ├── models/             # 数据模型和Pydantic模型
│   ├── services/           # 业务逻辑服务
│   └── utils/              # 工具函数
├── logs/                   # 日志文件存储目录
├── main.py                 # 主应用入口
├── requirements.txt        # 依赖包列表
├── start.bat               # 启动脚本
└── test_api.py             # API测试脚本
```

## 功能特点

1. 接收ba_school_info表中的ID数组，批量爬取学校详细信息
2. 自动检查登录状态，必要时进行登录
3. 解析页面数据，提取录取名单、复试名单、学校基本信息和调剂信息
4. 将数据保存到数据库中
5. 更新ba_school_info表中记录的状态
6. 完善的日志记录功能，分类记录不同模块的日志
7. 后台任务处理，爬取请求异步执行，不阻塞API响应
8. 任务状态跟踪，可随时查询任务进度和结果

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

### 方法1：使用批处理文件

直接双击 `start.bat` 文件启动服务。

### 方法2：使用命令行

```bash
cd get_detail_data
python main.py
```

## API接口

### 创建爬取任务

- **URL**: `/api/crawl_school_info`
- **方法**: POST
- **请求体**:
  ```json
  {
    "ids": [1214, 1215, 1216]  // 学校ID数组
  }
  ```
- **响应**:
  ```json
  {
    "task_id": "f8c3de3d-1234-5678-90ab-cdef01234567",
    "status": "pending",
    "message": "任务已创建，正在后台处理",
    "total": 3
  }
  ```

### 获取任务状态

- **URL**: `/api/task/{task_id}`
- **方法**: GET
- **响应**:
  ```json
  {
    "task_id": "f8c3de3d-1234-5678-90ab-cdef01234567",
    "status": "running",
    "total": 3,
    "processed": 1,
    "success": 1,
    "skipped": 0,
    "error": 0,
    "progress": 33.33,
    "elapsed_time": 5.67,
    "results": []
  }
  ```

### 获取任务列表

- **URL**: `/api/tasks`
- **方法**: GET
- **响应**:
  ```json
  {
    "tasks": [
      {
        "task_id": "f8c3de3d-1234-5678-90ab-cdef01234567",
        "status": "completed",
        "total": 3,
        "processed": 3,
        "success": 2,
        "skipped": 1,
        "error": 0,
        "progress": 100.0,
        "elapsed_time": 15.42
      },
      // 更多任务...
    ]
  }
  ```

### 检查登录状态

- **URL**: `/api/check_login_status`
- **方法**: GET
- **响应**:
  ```json
  {
    "is_logged_in": true,
    "message": "当前会话有效，无需重新登录",
    "login_time": "2023-05-20 14:30:45"
  }
  ```
  或（当需要在后台执行登录时）
  ```json
  {
    "is_logged_in": false,
    "message": "登录将在后台执行，请稍后再次检查登录状态",
    "login_time": null
  }
  ```
  或（当后台登录任务正在进行中）
  ```json
  {
    "is_logged_in": false,
    "message": "登录任务正在后台进行中，请稍后再试",
    "login_time": null
  }
  ```
  或（当登录失败时）
  ```json
  {
    "is_logged_in": false,
    "message": "登录失败，请稍后重试",
    "login_time": null
  }
  ```

## 测试API

### 测试同步API

使用提供的测试脚本 `test_api.py` 测试同步API功能：

```bash
python test_api.py
```

### 测试后台任务API

使用提供的测试脚本 `test_background_task.py` 测试后台任务API功能：

```bash
python test_background_task.py
```

### 测试登录状态API

使用提供的测试脚本 `test_login_status.py` 测试登录状态API功能：

```bash
python test_login_status.py
```

## 日志功能

系统提供了完善的日志记录功能，日志文件存储在 `logs` 目录下：

- `app.log`: 应用程序主日志
- `crawler.log`: 爬虫相关日志
- `db.log`: 数据库操作日志
- `api.log`: API请求日志
- `background.log`: 后台任务相关日志

日志按照大小进行分割，使用并发安全的日志处理器：
- 当日志文件超过配置的大小（默认10MB）时，创建新的日志文件
- 使用`concurrent_log_handler`库的`ConcurrentRotatingFileHandler`处理器，解决多进程/多线程写入日志时的文件锁定问题

日志分割的配置项在 `app/core/config.py` 文件的 `LOG_CONFIG` 中设置：
```python
LOG_CONFIG: Dict[str, Any] = {
    "max_bytes": 10485760,  # 10MB
    "backup_count": 20,     # 保留20个备份
    "when": "midnight",     # 每天午夜切换日志文件
    "interval": 1,          # 每1个时间单位切换一次
    "encoding": "utf-8"
}
```

## 数据库表结构

本服务涉及以下数据库表：

1. `ba_school_info`: 学校信息表
2. `ba_admission_list`: 录取名单表
3. `ba_retest_list`: 复试名单表
4. `ba_school_basic_info`: 学校详细信息表
5. `ba_transfer_info`: 调剂信息表

## 注意事项

1. 确保数据库连接信息正确配置在 `app/core/config.py` 文件中
2. 服务会自动处理登录状态，无需手动登录
3. 对于已经爬取过的学校（`detail_url_status=1`），服务会跳过处理
4. 服务会在爬取前后更新ba_school_info表中的爬取时间字段
