from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field

# 请求模型
class SchoolInfoRequest(BaseModel):
    """学校信息请求模型"""
    ids: List[int] = Field(..., description="学校ID列表")

# 响应模型
class SchoolInfoResult(BaseModel):
    """单个学校处理结果模型"""
    school_id: int = Field(..., description="学校ID")
    status: str = Field(..., description="处理状态: success, error, skipped")
    message: str = Field(..., description="处理结果消息")
    data_summary: Optional[Dict[str, Any]] = Field(None, description="数据摘要")

class SchoolInfoResponse(BaseModel):
    """学校信息响应模型"""
    results: List[SchoolInfoResult] = Field(..., description="处理结果列表")

# 登录状态响应模型
class LoginStatusResponse(BaseModel):
    """登录状态响应模型"""
    is_logged_in: bool = Field(..., description="是否已登录")
    message: str = Field(..., description="状态消息")
    login_time: Optional[str] = Field(None, description="登录时间")

# 任务模型
class TaskCreateResponse(BaseModel):
    """任务创建响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="任务消息")
    total: int = Field(..., description="总任务数")

class TaskStatusResponse(BaseModel):
    """任务状态响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    total: int = Field(..., description="总任务数")
    processed: int = Field(..., description="已处理任务数")
    success: int = Field(..., description="成功任务数")
    skipped: int = Field(..., description="跳过任务数")
    error: int = Field(..., description="失败任务数")
    progress: float = Field(..., description="任务进度百分比")
    elapsed_time: float = Field(..., description="任务运行时间(秒)")
    results: List[SchoolInfoResult] = Field(default_factory=list, description="处理结果列表")

class TaskSummary(BaseModel):
    """任务摘要模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    total: int = Field(..., description="总任务数")
    processed: int = Field(..., description="已处理任务数")
    success: int = Field(..., description="成功任务数")
    skipped: int = Field(..., description="跳过任务数")
    error: int = Field(..., description="失败任务数")
    progress: float = Field(..., description="任务进度百分比")
    elapsed_time: float = Field(..., description="任务运行时间(秒)")

class TaskListResponse(BaseModel):
    """任务列表响应模型"""
    tasks: List[TaskSummary] = Field(..., description="任务列表")

# 数据模型
class AdmissionData(BaseModel):
    """录取名单数据模型"""
    school_id: int
    year: int
    name: str
    college: str
    major_code: str
    major_name: str
    initial_score: Optional[float]
    retest_score: Optional[float]
    total_score: Optional[float]
    first_choice_school: str
    student_remark: Optional[str]

class RetestData(BaseModel):
    """复试名单数据模型"""
    school_id: int
    year: int
    name: str
    college: str
    major_code: str
    major_name: str
    politics_score: Optional[float]
    english_score: Optional[float]
    major1_score: Optional[float]
    major2_score: Optional[float]
    initial_score: Optional[float]
    volunteer_type: str
    admission_status: str

class SchoolBasicInfo(BaseModel):
    """学校基本信息数据模型"""
    school_id: int
    research_direction: str
    exam_range: str
    reference_books: str
    retest_content: str
    tuition_fee: str
    study_years: str
    accommodation: str
    admission_requirements: str

class TransferData(BaseModel):
    """调剂信息数据模型"""
    school_id: int
    year: int
    name: str
    transfer_school: str
    college: str
    major_code: str
    major_name: str
    initial_score: Optional[float]
    apply_school: str
    apply_major: str

class ParsedData(BaseModel):
    """解析后的完整数据模型"""
    admission_list: List[AdmissionData] = Field(default_factory=list)
    retest_list: List[RetestData] = Field(default_factory=list)
    basic_info: Optional[SchoolBasicInfo] = None
    transfer_info: List[TransferData] = Field(default_factory=list)
