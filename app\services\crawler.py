import time
import base64
import requests
from datetime import datetime
from typing import Optional, Dict, Any, Tuple, List
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.support import expected_conditions as EC
from bs4 import BeautifulSoup
from webdriver_manager.chrome import ChromeDriverManager
import re
from urllib.parse import urljoin
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

from app.core.config import settings
from app.core.logging_config import get_crawler_logger
from app.utils.captcha import recognize_captcha
from app.services.parser import parse_admission_list, parse_retest_list
from app.db import crud

logger = get_crawler_logger()

# 数据清理已移至主函数开始处，不再需要线程锁控制

# 全局变量，保存session
global_session = None
last_login_time = None

def is_session_valid() -> bool:
    """
    检查当前会话是否有效
    
    Returns:
        bool: 会话是否有效
    """
    global global_session, last_login_time
    
    # 如果没有会话或登录时间，则会话无效
    if not global_session or not last_login_time:
        logger.debug("会话无效: 没有会话或登录时间")
        return False
    
    # 如果登录时间超过30分钟，则会话无效
    if (datetime.now() - last_login_time).seconds > settings.CRAWLER["login_timeout"]:
        logger.debug(f"会话无效: 登录时间已超过{settings.CRAWLER['login_timeout']}秒")
        return False
    
    # 测试会话是否仍然有效
    try:
        response = global_session.get(settings.CRAWLER["test_url"])
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.debug("会话无效: 服务器返回登录超时")
            return False
        
        logger.debug("会话有效")
        return True
    except Exception as e:
        logger.error(f"检查会话有效性时出错: {str(e)}")
        return False

def login() -> Tuple[bool, Optional[requests.Session]]:
    """
    登录网站并获取会话
    
    Returns:
        Tuple[bool, Optional[requests.Session]]: 登录是否成功，以及会话对象
    """
    global global_session, last_login_time
    
    # 如果当前会话有效，直接返回
    if is_session_valid():
        logger.info("使用现有有效会话")
        return True, global_session
    
    logger.info("开始登录过程")
    
    # 初始化Selenium
    #service_selenium =  Service("/usr/local/bin/chromedriver")
    service_selenium =  Service(ChromeDriverManager().install())
    options_selenium = Options()
    options_selenium.add_argument("--headless")
    options_selenium.add_argument("--disable-gpu")
    options_selenium.add_argument("--no-sandbox")
    options_selenium.add_argument("--disable-dev-shm-usage")
    options_selenium.add_argument("--disable-notifications")
    options_selenium.add_argument("--disable-popup-blocking")
    options_selenium.add_argument("--disable-extensions")
    options_selenium.add_argument("--disable-application-cache")
    options_selenium.add_argument("--disable-plugins-discovery")
    options_selenium.add_argument(f"user-agent={settings.CRAWLER['user_agent']}")
    
    driver = webdriver.Chrome(service=service_selenium, options=options_selenium)
    
    try:
        url = settings.CRAWLER["login_url"]
        driver.get(url)
        wait = WebDriverWait(driver, 20)
        
        retry_count = 0
        while retry_count < settings.CRAWLER["retry_count"]:
            try:
                logger.debug(f"登录尝试 #{retry_count + 1}")
                
                # 等待验证码元素加载
                captcha_element = wait.until(EC.presence_of_element_located((By.ID, "imgCheckNo")))
                logger.debug("验证码元素已加载")
                
                # 截取验证码图片
                captcha_image_bytes = captcha_element.screenshot_as_png
                logger.debug("验证码图片已截取")
                
                # 将验证码图片转换为Base64编码
                imgstr = base64.b64encode(captcha_image_bytes).decode('utf-8')
                
                # 识别验证码
                recognized_text = recognize_captcha(imgstr)
                
                if recognized_text:
                    logger.info(f"验证码识别成功: {recognized_text}")
                    
                    # 填写登录表单
                    driver.find_element(By.ID, "TextBox1").send_keys(settings.USERNAME)
                    driver.find_element(By.ID, "tbxCheckNo").send_keys(recognized_text)
                    driver.find_element(By.ID, "TextBox2").send_keys(settings.PASSWORD)
                    driver.find_element(By.ID, "ImageButton1").click()
                    
                    # 等待页面跳转
                    wait.until(EC.url_changes(url))
                    logger.debug("登录后页面已跳转")
                    
                    # 获取cookies
                    cookie_list = driver.get_cookies()
                    
                    # 创建新的session并添加cookies
                    session = requests.Session()
                    request_cookies_dict = {}
                    for selenium_cookie in cookie_list:
                        request_cookies_dict[selenium_cookie['name']] = selenium_cookie['value']
                    session.cookies.update(request_cookies_dict)
                    session.headers.update({
                        'User-Agent': settings.CRAWLER["user_agent"],
                    })
                    
                    # 测试登录状态
                    response = session.get(settings.CRAWLER["test_url"])
                    
                    if "登录超时" in response.text or "请重新登录" in response.text:
                        logger.warning("登录后检测到会话超时，重新尝试")
                        driver.get(url)
                        retry_count += 1
                        time.sleep(settings.CRAWLER["retry_interval"])
                        continue
                    else:
                        logger.info("登录成功")
                        
                        # 更新全局会话和登录时间
                        global_session = session
                        last_login_time = datetime.now()
                        
                        driver.quit()
                        return True, session
                else:
                    logger.warning("验证码识别失败，刷新页面重试")
                    driver.refresh()
                    retry_count += 1
                    time.sleep(settings.CRAWLER["retry_interval"])
            except Exception as e:
                if hasattr(e, 'alert_text') and "验证码" in e.alert_text.strip():
                    logger.warning(f"验证码错误: {e.alert_text.strip()}")
                    driver.refresh()
                    retry_count += 1
                    time.sleep(settings.CRAWLER["retry_interval"])
                else:
                    logger.error(f"登录过程中发生错误: {str(e)}")
                    driver.quit()
                    return False, None
        
        logger.error(f"登录失败: 超过最大重试次数 {settings.CRAWLER['retry_count']}")
        driver.quit()
        return False, None
    
    except Exception as e:
        logger.error(f"登录过程中发生未捕获的错误: {str(e)}")
        driver.quit()
        return False, None

def extract_form_data(html_content: str) -> Dict[str, str]:
    """
    从HTML页面中提取ASP.NET表单数据

    Args:
        html_content: HTML页面内容

    Returns:
        Dict[str, str]: 表单数据字典
    """
    form_data = {}

    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 提取__VIEWSTATE
        viewstate = soup.find('input', {'name': '__VIEWSTATE'})
        if viewstate and viewstate.get('value'):
            form_data['__VIEWSTATE'] = viewstate['value']

        # 提取__VIEWSTATEGENERATOR
        viewstate_generator = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
        if viewstate_generator and viewstate_generator.get('value'):
            form_data['__VIEWSTATEGENERATOR'] = viewstate_generator['value']

        # 提取__EVENTVALIDATION
        event_validation = soup.find('input', {'name': '__EVENTVALIDATION'})
        if event_validation and event_validation.get('value'):
            form_data['__EVENTVALIDATION'] = event_validation['value']

        logger.debug(f"提取表单数据成功: VIEWSTATE长度={len(form_data.get('__VIEWSTATE', ''))}")

    except Exception as e:
        logger.error(f"提取表单数据失败: {str(e)}")

    return form_data

def has_next_page(html_content: str, grid_view_id: str) -> Tuple[bool, Optional[str]]:
    """
    检查GridView是否有下一页，并返回下一页的事件目标

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID（如GridView1, GridView2等）

    Returns:
        Tuple[bool, Optional[str]]: 是否有下一页，以及下一页的事件目标
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找指定的GridView表格
        grid_view = soup.find('table', {'id': grid_view_id})
        if not grid_view:
            logger.debug(f"未找到GridView: {grid_view_id}")
            return False, None

        logger.debug(f"找到GridView: {grid_view_id}")

        # 查找分页控件行
        # 方法1: 查找包含分页标签的行 (如 "第1页/共18页")
        page_label_id = f"{grid_view_id}_lblPage"
        pager_row = grid_view.find('span', {'id': page_label_id})
        if pager_row:
            pager_row = pager_row.find_parent('tr')
            logger.debug(f"{grid_view_id} 通过页面标签找到分页行")

        # 方法2: 查找包含下一页链接ID的行
        if not pager_row:
            next_link_id = f"{grid_view_id}_lbnNext"
            next_link_element = grid_view.find('a', {'id': next_link_id})
            if next_link_element:
                pager_row = next_link_element.find_parent('tr')
                logger.debug(f"{grid_view_id} 通过下一页链接找到分页行")

        # 方法3: 查找包含分页相关链接的行
        if not pager_row:
            all_rows = grid_view.find_all('tr')
            logger.debug(f"{grid_view_id} 表格共有 {len(all_rows)} 行，开始逐行查找分页控件")

            for i, row in enumerate(reversed(all_rows)):  # 从最后一行开始查找
                # 查找包含分页相关链接的行
                links = row.find_all('a')
                if links:
                    for link in links:
                        href = link.get('href', '')
                        link_id = link.get('id', '')
                        if ('lbn' in href and grid_view_id in href) or ('lbn' in link_id and grid_view_id in link_id):
                            logger.debug(f"{grid_view_id} 第 {len(all_rows)-i} 行找到分页链接: id={link_id}, href={href}")
                            pager_row = row
                            break
                if pager_row:
                    break

        if not pager_row:
            logger.debug(f"未找到分页控件: {grid_view_id}")
            return False, None

        logger.debug(f"找到分页控件行: {grid_view_id}")

        # 查找"下一页"链接 - 使用更精确的ID匹配
        next_link_id = f"{grid_view_id}_lbnNext"
        next_link = pager_row.find('a', {'id': next_link_id})

        if not next_link:
            # 备用方法：通过href中的事件目标查找
            next_link = pager_row.find('a', href=lambda x: x and f'{grid_view_id}$ctl25$lbnNext' in x)

        if not next_link:
            # 最后尝试：查找包含lbnNext的链接
            all_links = pager_row.find_all('a')
            logger.debug(f"{grid_view_id} 分页行包含 {len(all_links)} 个链接")
            for link in all_links:
                href = link.get('href', '')
                link_id = link.get('id', '')
                text = link.get_text(strip=True)
                logger.debug(f"{grid_view_id} 链接: id={link_id}, href={href}, text={text}")
                if 'lbnNext' in href or 'lbnNext' in link_id:
                    next_link = link
                    break

        if not next_link:
            logger.debug(f"未找到下一页链接: {grid_view_id}")
            return False, None

        # 提取事件目标
        href = next_link.get('href', '')
        logger.debug(f"{grid_view_id} 下一页链接href: {href}")

        # href格式通常是: javascript:__doPostBack('GridView1$ctl25$lbnNext','')
        match = re.search(r"__doPostBack\('([^']+)'", href)
        if match:
            event_target = match.group(1)
            logger.info(f"找到下一页链接: {grid_view_id}, 事件目标: {event_target}")
            return True, event_target

        logger.debug(f"无法解析下一页链接: {grid_view_id}, href: {href}")
        return False, None

    except Exception as e:
        logger.error(f"检查分页时发生错误: {str(e)}")
        return False, None

def merge_table_data(base_html: str, additional_html: str, grid_view_id: str) -> str:
    """
    将额外页面的表格数据合并到基础HTML中

    Args:
        base_html: 基础HTML内容（第一页）
        additional_html: 额外页面的HTML内容
        grid_view_id: GridView的ID

    Returns:
        str: 合并后的HTML内容
    """
    try:
        base_soup = BeautifulSoup(base_html, 'html.parser')
        additional_soup = BeautifulSoup(additional_html, 'html.parser')

        # 找到基础HTML中的目标表格
        base_table = base_soup.find('table', {'id': grid_view_id})
        if not base_table:
            logger.warning(f"基础HTML中未找到表格: {grid_view_id}")
            return base_html

        # 找到额外HTML中的目标表格
        additional_table = additional_soup.find('table', {'id': grid_view_id})
        if not additional_table:
            logger.warning(f"额外HTML中未找到表格: {grid_view_id}")
            return base_html

        # 获取额外表格中的数据行（跳过表头和分页行）
        additional_rows = additional_table.find_all('tr')
        data_rows = []

        for row in additional_rows:
            # 跳过表头行（通常包含th标签）
            if row.find('th'):
                continue
            # 跳过分页行（通常包含分页控件）
            if row.find('a', href=lambda x: x and ('lbn' in x or 'Page' in x)):
                continue
            # 跳过空行或只包含空白的行
            if not row.get_text(strip=True):
                continue

            data_rows.append(row)

        if not data_rows:
            logger.debug(f"额外页面中没有找到数据行: {grid_view_id}")
            return base_html

        # 找到基础表格中插入数据的位置（在最后一个数据行之后，分页行之前）
        base_rows = base_table.find_all('tr')
        insert_position = None

        for i, row in enumerate(reversed(base_rows)):
            # 找到分页行
            if row.find('a', href=lambda x: x and ('lbn' in x or 'Page' in x)):
                insert_position = len(base_rows) - i - 1
                break

        if insert_position is None:
            # 如果没有找到分页行，插入到表格末尾
            insert_position = len(base_rows)

        # 插入数据行 - 使用更简单的方法直接复制HTML
        for i, data_row in enumerate(data_rows):
            # 创建新行的HTML字符串
            row_html = str(data_row)
            # 使用BeautifulSoup解析这个HTML字符串
            new_row_soup = BeautifulSoup(row_html, 'html.parser')
            new_row = new_row_soup.find('tr')

            if new_row:
                # 找到插入位置的行
                base_rows = base_table.find_all('tr')
                if insert_position + i < len(base_rows):
                    # 在指定位置后插入
                    base_rows[insert_position + i].insert_after(new_row)
                else:
                    # 在表格末尾添加
                    base_table.append(new_row)

        logger.info(f"成功合并 {len(data_rows)} 行数据到表格: {grid_view_id}")
        return str(base_soup)

    except Exception as e:
        logger.error(f"合并表格数据时发生错误: {str(e)}")
        return base_html

def parse_and_save_page_data(html_content: str, grid_view_id: str, school_id: int, page_num: int) -> bool:
    """
    解析单页数据并立即保存到数据库

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID
        school_id: 学校ID
        page_num: 页码

    Returns:
        bool: 保存是否成功
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 根据GridView ID确定年份和数据类型
        year_mapping = {
            'GridView1': (2024, 'admission'),  # 2024年拟录取名单
            'GridView2': (2024, 'retest'),     # 2024年复试名单
            'GridView5': (2023, 'retest'),     # 2023年复试名单
            'GridView6': (2023, 'admission'),  # 2023年拟录取名单
            'GridView7': (2025, 'retest'),     # 2025年复试名单
            'GridView8': (2025, 'admission'),  # 2025年拟录取名单
        }

        if grid_view_id not in year_mapping:
            logger.warning(f"未知的GridView ID: {grid_view_id}")
            return False

        year, data_type = year_mapping[grid_view_id]

        # 解析数据（只做追加，不删除旧数据）
        if data_type == 'admission':
            data_list = parse_admission_list(soup, school_id, year)
            if data_list:
                # 保存录取名单数据（纯追加模式）
                crud.save_admission_list_append(data_list, school_id, page_num, grid_view_id, False)
                logger.info(f"{grid_view_id} 第{page_num}页录取名单数据保存成功: {len(data_list)}条")

        elif data_type == 'retest':
            data_list = parse_retest_list(soup, school_id, year)
            if data_list:
                # 保存复试名单数据（纯追加模式）
                crud.save_retest_list_append(data_list, school_id, page_num, grid_view_id, False)
                logger.info(f"{grid_view_id} 第{page_num}页复试名单数据保存成功: {len(data_list)}条")

        return True

    except Exception as e:
        logger.error(f"解析并保存 {grid_view_id} 第{page_num}页数据时发生错误: {str(e)}")
        return False

def fetch_gridview_pagination(url: str, grid_view_id: str, session: requests.Session, school_id: int) -> Tuple[bool, int]:
    """
    获取单个GridView的所有分页数据，并逐页保存到数据库

    Args:
        url: 页面URL
        grid_view_id: GridView的ID
        session: 已登录的session
        school_id: 学校ID

    Returns:
        Tuple[bool, int]: 获取是否成功，以及总页数
    """
    try:
        logger.info(f"开始处理 {grid_view_id} 的分页爬取和数据保存")

        # 获取第一页内容
        response = session.get(url)
        if response.status_code != 200:
            logger.error(f"{grid_view_id} 第一页请求失败: {response.status_code}")
            return False, 0

        # 检查是否需要重新登录
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.warning(f"{grid_view_id} 检测到会话超时")
            return False, 0

        current_html = response.text
        page_num = 1
        total_saved_records = 0

        logger.info(f"{grid_view_id} 第一页获取成功")

        # 解析并保存第一页数据
        logger.info(f"{grid_view_id} 开始解析并保存第一页数据")
        if parse_and_save_page_data(current_html, grid_view_id, school_id, page_num):
            total_saved_records += 1
            logger.info(f"{grid_view_id} 第一页数据保存成功")
        else:
            logger.warning(f"{grid_view_id} 第一页数据保存失败")

        while True:
            # 检查当前页面是否有下一页
            logger.debug(f"{grid_view_id} 检查第 {page_num} 页是否有下一页")
            has_next, event_target = has_next_page(current_html, grid_view_id)
            logger.info(f"{grid_view_id} 第 {page_num} 页分页检查结果: has_next={has_next}, event_target={event_target}")

            if not has_next:
                logger.info(f"{grid_view_id} 没有更多分页，共处理 {page_num} 页")
                break

            # 提取表单数据
            form_data = extract_form_data(current_html)
            if not form_data.get('__VIEWSTATE'):
                logger.warning(f"{grid_view_id} 无法提取表单数据，停止分页")
                break

            # 构造POST请求数据
            post_data = {
                '__EVENTTARGET': event_target,
                '__EVENTARGUMENT': '',
                '__VIEWSTATE': form_data['__VIEWSTATE'],
                '__VIEWSTATEGENERATOR': form_data.get('__VIEWSTATEGENERATOR', ''),
                '__EVENTVALIDATION': form_data.get('__EVENTVALIDATION', ''),
            }

            # 发送POST请求获取下一页
            logger.debug(f"获取 {grid_view_id} 第 {page_num + 1} 页")

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'User-Agent': settings.CRAWLER["user_agent"],
                'Referer': url
            }

            next_response = session.post(url, data=post_data, headers=headers)

            if next_response.status_code != 200:
                logger.warning(f"{grid_view_id} 第 {page_num + 1} 页请求失败: {next_response.status_code}")
                break

            # 检查响应是否有效
            if "登录超时" in next_response.text or "请重新登录" in next_response.text:
                logger.warning(f"{grid_view_id} 第 {page_num + 1} 页检测到会话超时")
                break

            current_html = next_response.text
            page_num += 1

            # 解析并保存当前页数据
            if parse_and_save_page_data(current_html, grid_view_id, school_id, page_num):
                total_saved_records += 1

            logger.info(f"{grid_view_id} 第 {page_num} 页数据获取并保存成功")

            # 防止无限循环，最多获取100页
            if page_num >= 100:
                logger.warning(f"{grid_view_id} 已达到最大页数限制(100页)")
                break

            # 添加延迟避免请求过快
            time.sleep(0.3)

        logger.info(f"{grid_view_id} 分页处理完成，共获取 {page_num} 页数据，保存 {total_saved_records} 页")
        return True, page_num

    except Exception as e:
        logger.error(f"处理 {grid_view_id} 分页时发生错误: {str(e)}")
        return False, 0

def fetch_page_with_pagination(url: str, school_id: int = None) -> Tuple[bool, Optional[str]]:
    """
    获取页面内容，支持多线程分页数据的完整获取

    Args:
        url: 页面URL

    Returns:
        Tuple[bool, Optional[str]]: 获取是否成功，以及完整的页面内容
    """
    global global_session

    # 确保已登录
    success, session = login()
    if not success:
        logger.error("获取页面失败: 登录失败")
        return False, None

    try:
        # 获取第一页内容
        logger.debug(f"开始获取页面: {url}")
        response = session.get(url)

        # 检查是否需要重新登录
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.warning("获取页面时检测到会话超时，尝试重新登录")

            # 强制重新登录
            global last_login_time
            global_session = None
            last_login_time = None

            success, session = login()
            if not success:
                logger.error("重新登录失败")
                return False, None

            # 重新获取页面
            response = session.get(url)

            # 再次检查
            if "登录超时" in response.text or "请重新登录" in response.text:
                logger.error("重新登录后仍然检测到会话超时")
                return False, None

        base_html = response.text
        logger.info(f"第一页获取成功: {url}")

        # 测试分页检测功能
        test_pagination_detection(base_html)

        # 需要检查分页的GridView列表
        grid_views_to_check = [
            'GridView1',  # 2024年拟录取名单
            'GridView2',  # 2024年复试名单
            'GridView5',  # 2023年复试名单
            'GridView6',  # 2023年拟录取名单
            'GridView7',  # 2025年复试名单
            'GridView8'   # 2025年拟录取名单
        ]

        logger.info(f"开始多线程分页爬取，GridView列表: {grid_views_to_check}")

        # 如果没有提供school_id，则使用传统的HTML合并方式
        if school_id is None:
            logger.warning("未提供school_id，使用传统HTML合并方式")
            # 这里保留原有的HTML合并逻辑作为备用
            return True, base_html

        # 在开始解析数据之前，先删除该学校的旧数据
        try:
            from app.db import crud
            logger.info(f"开始清理学校旧数据: ID={school_id}")

            # 删除录取名单旧数据
            with crud.db_cursor() as cursor:
                cursor.execute("DELETE FROM ba_admission_list WHERE school_id = %s", (school_id,))
                logger.info(f"删除学校旧的录取名单数据: ID={school_id}")

            # 删除复试名单旧数据
            with crud.db_cursor() as cursor:
                cursor.execute("DELETE FROM ba_retest_list WHERE school_id = %s", (school_id,))
                logger.info(f"删除学校旧的复试名单数据: ID={school_id}")

        except Exception as e:
            logger.error(f"清理学校旧数据失败: ID={school_id}, 错误={str(e)}")
            return False, None

        # 使用线程池并行处理每个GridView的分页，并逐页保存数据
        gridview_results = {}

        with ThreadPoolExecutor(max_workers=6) as executor:
            # 为每个GridView创建独立的session副本
            future_to_gridview = {}

            for grid_view_id in grid_views_to_check:
                # 创建session副本（共享cookies）
                session_copy = requests.Session()
                session_copy.cookies.update(session.cookies)
                session_copy.headers.update(session.headers)

                # 提交任务到线程池，传递school_id
                future = executor.submit(fetch_gridview_pagination, url, grid_view_id, session_copy, school_id)
                future_to_gridview[future] = grid_view_id

            # 收集结果
            for future in as_completed(future_to_gridview):
                grid_view_id = future_to_gridview[future]
                try:
                    success, total_pages = future.result()
                    if success:
                        gridview_results[grid_view_id] = total_pages
                        logger.info(f"{grid_view_id} 多线程分页爬取成功，共处理 {total_pages} 页")
                    else:
                        logger.warning(f"{grid_view_id} 多线程分页爬取失败")
                except Exception as e:
                    logger.error(f"{grid_view_id} 多线程分页爬取异常: {str(e)}")

        # 统计总体结果
        total_pages_processed = sum(gridview_results.values())
        successful_gridviews = len([gv for gv, pages in gridview_results.items() if pages > 0])

        logger.info(f"多线程分页爬取完成: {url}")
        logger.info(f"处理结果: 成功处理 {successful_gridviews}/{len(grid_views_to_check)} 个GridView，共 {total_pages_processed} 页数据")

        # 数据已经直接保存到数据库，返回基础HTML即可
        return True, base_html

    except Exception as e:
        logger.error(f"获取页面时发生错误: {str(e)}")
        return False, None

def test_pagination_detection(html_content: str) -> None:
    """
    测试分页检测功能
    """
    logger.info("=== 开始测试分页检测 ===")

    grid_views_to_test = ['GridView1', 'GridView2', 'GridView5', 'GridView6', 'GridView7', 'GridView8']

    for grid_view_id in grid_views_to_test:
        has_next, event_target = has_next_page(html_content, grid_view_id)
        logger.info(f"测试 {grid_view_id}: has_next={has_next}, event_target={event_target}")

    logger.info("=== 分页检测测试完成 ===")

def fetch_page(url: str, school_id: int = None) -> Tuple[bool, Optional[str]]:
    """
    获取页面内容（兼容性函数，调用支持分页的版本）

    Args:
        url: 页面URL
        school_id: 学校ID（可选，用于逐页保存数据）

    Returns:
        Tuple[bool, Optional[str]]: 获取是否成功，以及页面内容
    """
    return fetch_page_with_pagination(url, school_id)

def get_current_year() -> int:
    """
    获取当前年份

    Returns:
        int: 当前年份
    """
    from datetime import datetime
    return datetime.now().year

def parse_and_save_page_data_backup(html_content: str, grid_view_id: str, school_id: int, page_num: int) -> bool:
    """
    解析单页数据并立即保存到备份表

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID
        school_id: 学校ID
        page_num: 页码

    Returns:
        bool: 保存是否成功
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 根据GridView ID确定年份和数据类型
        year_mapping = {
            'GridView1': (2024, 'admission'),  # 2024年拟录取名单
            'GridView2': (2024, 'retest'),     # 2024年复试名单
            'GridView5': (2023, 'retest'),     # 2023年复试名单
            'GridView6': (2023, 'admission'),  # 2023年拟录取名单
            'GridView7': (2025, 'retest'),     # 2025年复试名单
            'GridView8': (2025, 'admission'),  # 2025年拟录取名单
        }

        if grid_view_id not in year_mapping:
            logger.warning(f"未知的GridView ID: {grid_view_id}")
            return False

        year, data_type = year_mapping[grid_view_id]

        # 解析数据（只做追加，不删除旧数据）
        if data_type == 'admission':
            data_list = parse_admission_list(soup, school_id, year)
            if data_list:
                # 保存录取名单数据到备份表
                from app.db import crud
                crud.save_admission_list_backup(data_list, school_id)
                logger.info(f"{grid_view_id} 第{page_num}页录取名单数据保存到备份表成功: {len(data_list)}条")

        elif data_type == 'retest':
            data_list = parse_retest_list(soup, school_id, year)
            if data_list:
                # 保存复试名单数据到备份表
                from app.db import crud
                crud.save_retest_list_backup(data_list, school_id)
                logger.info(f"{grid_view_id} 第{page_num}页复试名单数据保存到备份表成功: {len(data_list)}条")

        return True

    except Exception as e:
        logger.error(f"解析并保存 {grid_view_id} 第{page_num}页数据到备份表时发生错误: {str(e)}")
        return False

def get_total_pages(html_content: str, grid_view_id: str) -> int:
    """
    获取指定GridView的总页数

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID

    Returns:
        int: 总页数，如果无法获取则返回1
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找指定的GridView表格
        grid_view = soup.find('table', {'id': grid_view_id})
        if not grid_view:
            logger.debug(f"未找到GridView: {grid_view_id}")
            return 1

        # 查找分页标签 (如 "第1页/共18页")
        page_label_id = f"{grid_view_id}_lblPage"
        page_label = grid_view.find('span', {'id': page_label_id})

        if page_label:
            page_text = page_label.get_text(strip=True)
            logger.debug(f"{grid_view_id} 分页标签文本: {page_text}")

            # 解析总页数，格式通常是 "第1页/共18页"
            import re
            match = re.search(r'共(\d+)页', page_text)
            if match:
                total_pages = int(match.group(1))
                logger.info(f"{grid_view_id} 总页数: {total_pages}")
                return total_pages

        logger.debug(f"{grid_view_id} 无法获取总页数，默认为1页")
        return 1

    except Exception as e:
        logger.error(f"获取 {grid_view_id} 总页数时发生错误: {str(e)}")
        return 1

def find_last_page_link(html_content: str, grid_view_id: str) -> str:
    """
    查找尾页链接的事件目标

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID

    Returns:
        str: 尾页的事件目标，如果找不到则返回空字符串
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找指定的GridView表格
        grid_view = soup.find('table', {'id': grid_view_id})
        if not grid_view:
            logger.debug(f"未找到GridView: {grid_view_id}")
            return ""

        # 查找尾页链接，格式如：<a id="GridView8_lbnLast" href="javascript:__doPostBack(&#39;GridView8$ctl25$lbnLast&#39;,&#39;&#39;)">尾页</a>
        last_link_id = f"{grid_view_id}_lbnLast"
        last_link = grid_view.find('a', {'id': last_link_id})

        if last_link:
            href = last_link.get('href', '')
            logger.debug(f"{grid_view_id} 尾页链接href: {href}")

            # 解析事件目标，格式：javascript:__doPostBack('GridView8$ctl25$lbnLast','')
            import re
            match = re.search(r"__doPostBack\('([^']+)'", href)
            if match:
                event_target = match.group(1)
                logger.info(f"找到尾页链接: {grid_view_id}, 事件目标: {event_target}")
                return event_target

        # 备用方法：查找包含"尾页"、"末页"、"Last"等文本的链接
        all_links = grid_view.find_all('a')
        for link in all_links:
            text = link.get_text(strip=True)
            href = link.get('href', '')

            if any(keyword in text for keyword in ['尾页', '末页', 'Last', '>>']) or 'lbnLast' in href:
                match = re.search(r"__doPostBack\('([^']+)'", href)
                if match:
                    event_target = match.group(1)
                    logger.info(f"通过文本找到尾页链接: {grid_view_id}, 事件目标: {event_target}")
                    return event_target

        logger.debug(f"未找到尾页链接: {grid_view_id}")
        return ""

    except Exception as e:
        logger.error(f"查找尾页链接时发生错误: {str(e)}")
        return ""

def crawl_specific_page(url: str, grid_view_id: str, session, school_id: int, target_page: int, form_data: dict, first_page_html: str = None) -> bool:
    """
    使用尾页按钮直接跳转到最后一页，或使用页面跳转功能跳转到指定页面

    Args:
        url: 页面URL
        grid_view_id: GridView的ID
        session: 已登录的session
        school_id: 学校ID
        target_page: 目标页码
        form_data: 表单数据
        first_page_html: 第一页的HTML内容（用于查找尾页链接）

    Returns:
        bool: 爬取是否成功
    """
    try:
        # 如果是跳转到最后一页，优先尝试使用尾页按钮
        if first_page_html:
            total_pages = get_total_pages(first_page_html, grid_view_id)
            if target_page == total_pages and total_pages > 1:
                # 尝试使用尾页按钮直接跳转
                last_page_event_target = find_last_page_link(first_page_html, grid_view_id)

                if last_page_event_target:
                    logger.info(f"{grid_view_id} 使用尾页按钮直接跳转到最后一页")
                    post_data = {
                        '__EVENTTARGET': last_page_event_target,
                        '__EVENTARGUMENT': '',
                        '__VIEWSTATE': form_data['__VIEWSTATE'],
                        '__VIEWSTATEGENERATOR': form_data.get('__VIEWSTATEGENERATOR', ''),
                        '__EVENTVALIDATION': form_data.get('__EVENTVALIDATION', ''),
                    }

                    logger.debug(f"{grid_view_id} 尾页按钮POST数据: {post_data}")

                    # 发送POST请求获取最后一页
                    response = session.post(url, data=post_data)
                    if response.status_code != 200:
                        logger.error(f"{grid_view_id} 尾页请求失败: {response.status_code}")
                        return False

                    # 检查是否需要重新登录
                    if "登录超时" in response.text or "请重新登录" in response.text:
                        logger.warning(f"{grid_view_id} 尾页检测到会话超时")
                        return False

                    # 验证是否成功跳转到最后一页
                    current_page = get_current_page_number(response.text, grid_view_id)
                    logger.info(f"{grid_view_id} 尾页跳转结果：当前页码 {current_page}，总页数 {total_pages}")

                    # 解析并保存数据
                    logger.info(f"{grid_view_id} 开始解析并保存尾页数据到备份表")
                    if parse_and_save_page_data_backup(response.text, grid_view_id, school_id, target_page):
                        logger.info(f"{grid_view_id} 尾页数据保存到备份表成功")
                        return True
                    else:
                        logger.warning(f"{grid_view_id} 尾页数据保存到备份表失败")
                        return False

        # 备用方法：使用页面跳转功能
        logger.info(f"{grid_view_id} 使用页面跳转功能跳转到第{target_page}页")

        post_data = {
            '__EVENTTARGET': f'{grid_view_id}$ctl25$Button1',  # 跳转按钮
            '__EVENTARGUMENT': '',
            '__VIEWSTATE': form_data['__VIEWSTATE'],
            '__VIEWSTATEGENERATOR': form_data.get('__VIEWSTATEGENERATOR', ''),
            '__EVENTVALIDATION': form_data.get('__EVENTVALIDATION', ''),
            f'{grid_view_id}$ctl25$inPageNum': str(target_page),  # 目标页码
            f'{grid_view_id}$ctl25$Button1': '跳转'  # 跳转按钮文本
        }

        logger.debug(f"{grid_view_id} 页面跳转POST数据: {post_data}")

        # 发送POST请求获取指定页面
        response = session.post(url, data=post_data)
        if response.status_code != 200:
            logger.error(f"{grid_view_id} 第{target_page}页请求失败: {response.status_code}")
            return False

        # 检查是否需要重新登录
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.warning(f"{grid_view_id} 第{target_page}页检测到会话超时")
            return False

        # 验证是否成功跳转到目标页面
        current_page = get_current_page_number(response.text, grid_view_id)
        if current_page != target_page:
            logger.warning(f"{grid_view_id} 跳转失败，期望第{target_page}页，实际第{current_page}页")
        else:
            logger.info(f"{grid_view_id} 成功跳转到第{target_page}页")

        # 解析并保存数据
        logger.info(f"{grid_view_id} 开始解析并保存第{target_page}页数据到备份表")
        if parse_and_save_page_data_backup(response.text, grid_view_id, school_id, target_page):
            logger.info(f"{grid_view_id} 第{target_page}页数据保存到备份表成功")
            return True
        else:
            logger.warning(f"{grid_view_id} 第{target_page}页数据保存到备份表失败")
            return False

    except Exception as e:
        logger.error(f"爬取 {grid_view_id} 第{target_page}页时发生错误: {str(e)}")
        return False

def get_current_page_number(html_content: str, grid_view_id: str) -> int:
    """
    获取当前页面的页码

    Args:
        html_content: HTML页面内容
        grid_view_id: GridView的ID

    Returns:
        int: 当前页码，如果无法获取则返回0
    """
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找页面标签 (如 "第1页/共18页")
        page_label_id = f"{grid_view_id}_lblPage"
        page_label = soup.find('span', {'id': page_label_id})

        if page_label:
            page_text = page_label.get_text(strip=True)
            logger.debug(f"{grid_view_id} 分页标签文本: {page_text}")

            # 解析当前页码，格式通常是 "第1页/共18页"
            import re
            match = re.search(r'第(\d+)页', page_text)
            if match:
                current_page = int(match.group(1))
                logger.debug(f"{grid_view_id} 当前页码: {current_page}")
                return current_page

        logger.debug(f"{grid_view_id} 无法获取当前页码")
        return 0

    except Exception as e:
        logger.error(f"获取 {grid_view_id} 当前页码时发生错误: {str(e)}")
        return 0

def crawl_first_and_last_page_limited(url: str, grid_view_id: str, session, school_id: int) -> Tuple[bool, int]:
    """
    使用多线程直接爬取指定GridView的第一页和最后一页数据并保存到备份表

    Args:
        url: 页面URL
        grid_view_id: GridView的ID
        session: 已登录的session
        school_id: 学校ID

    Returns:
        Tuple[bool, int]: 获取是否成功，以及总页数
    """
    try:
        logger.info(f"开始处理 {grid_view_id} 的限制分页爬取（仅第一页和最后一页，多线程）")

        # 获取第一页内容
        response = session.get(url)
        if response.status_code != 200:
            logger.error(f"{grid_view_id} 第一页请求失败: {response.status_code}")
            return False, 0

        # 检查是否需要重新登录
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.warning(f"{grid_view_id} 检测到会话超时")
            return False, 0

        first_page_html = response.text
        logger.info(f"{grid_view_id} 第一页获取成功")

        # 获取总页数
        total_pages = get_total_pages(first_page_html, grid_view_id)
        logger.info(f"{grid_view_id} 总页数: {total_pages}")

        # 如果只有一页，直接处理第一页
        if total_pages <= 1:
            logger.info(f"{grid_view_id} 只有一页数据，直接处理")
            if parse_and_save_page_data_backup(first_page_html, grid_view_id, school_id, 1):
                logger.info(f"{grid_view_id} 第一页数据保存到备份表成功")
                return True, total_pages
            else:
                logger.warning(f"{grid_view_id} 第一页数据保存到备份表失败")
                return False, total_pages

        # 提取表单数据用于后续请求
        form_data = extract_form_data(first_page_html)
        if not form_data.get('__VIEWSTATE'):
            logger.warning(f"{grid_view_id} 无法提取表单数据，只处理第一页")
            if parse_and_save_page_data_backup(first_page_html, grid_view_id, school_id, 1):
                logger.info(f"{grid_view_id} 第一页数据保存到备份表成功")
                return True, total_pages
            else:
                return False, total_pages

        # 使用多线程同时处理第一页和最后一页
        import concurrent.futures
        import threading

        thread_lock = threading.Lock()
        results = {}

        def process_page(page_num, html_content=None):
            try:
                with thread_lock:
                    logger.info(f"{grid_view_id} 开始处理第{page_num}页")

                if page_num == 1:
                    # 第一页直接使用已获取的HTML
                    success = parse_and_save_page_data_backup(html_content, grid_view_id, school_id, page_num)
                else:
                    # 最后一页需要发送请求获取，优先使用尾页按钮，备用页面跳转功能
                    success = crawl_specific_page(url, grid_view_id, session, school_id, page_num, form_data, first_page_html)

                with thread_lock:
                    results[page_num] = success
                    logger.info(f"{grid_view_id} 第{page_num}页处理完成: {success}")

            except Exception as e:
                with thread_lock:
                    logger.error(f"{grid_view_id} 处理第{page_num}页时发生错误: {str(e)}")
                    results[page_num] = False

        # 使用线程池同时处理第一页和最后一页
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            futures = []

            # 提交第一页任务
            futures.append(executor.submit(process_page, 1, first_page_html))

            # 提交最后一页任务（如果不是第一页）
            if total_pages > 1:
                futures.append(executor.submit(process_page, total_pages))

            # 等待所有任务完成
            concurrent.futures.wait(futures)

        # 统计结果
        success_count = sum(1 for success in results.values() if success)
        total_tasks = len(results)

        logger.info(f"{grid_view_id} 多线程限制分页处理完成，总页数: {total_pages}，成功处理: {success_count}/{total_tasks}")

        # 如果至少有一个页面成功，就认为整体成功
        overall_success = success_count > 0
        return overall_success, total_pages

    except Exception as e:
        logger.error(f"处理 {grid_view_id} 限制分页时发生错误: {str(e)}")
        return False, 0

def fetch_page_with_pagination_limited(url: str, school_id: int = None) -> Tuple[bool, Optional[str]]:
    """
    获取页面内容，只爬取当年的拟录取名单和复试名单，且只爬第一页和最后一页

    Args:
        url: 页面URL
        school_id: 学校ID

    Returns:
        Tuple[bool, Optional[str]]: 获取是否成功，以及第一页的页面内容
    """
    global global_session

    # 确保已登录
    success, session = login()
    if not success:
        logger.error("获取页面失败: 登录失败")
        return False, None

    try:
        # 获取第一页内容
        logger.debug(f"开始获取页面: {url}")
        response = session.get(url)

        # 检查是否需要重新登录
        if "登录超时" in response.text or "请重新登录" in response.text:
            logger.warning("获取页面时检测到会话超时，尝试重新登录")

            # 强制重新登录
            global last_login_time
            global_session = None
            last_login_time = None

            success, session = login()
            if not success:
                logger.error("重新登录失败")
                return False, None

            # 重新获取页面
            response = session.get(url)

            # 再次检查
            if "登录超时" in response.text or "请重新登录" in response.text:
                logger.error("重新登录后仍然检测到会话超时")
                return False, None

        base_html = response.text
        logger.info(f"第一页获取成功: {url}")

        # 获取当前年份
        current_year = get_current_year()
        logger.info(f"当前年份: {current_year}")

        # 只处理当年的GridView列表
        current_year_grid_views = []
        if current_year == 2025:
            current_year_grid_views = [
                'GridView7',  # 2025年复试名单
                'GridView8'   # 2025年拟录取名单
            ]
        elif current_year == 2024:
            current_year_grid_views = [
                'GridView1',  # 2024年拟录取名单
                'GridView2'   # 2024年复试名单
            ]
        elif current_year == 2023:
            current_year_grid_views = [
                'GridView5',  # 2023年复试名单
                'GridView6'   # 2023年拟录取名单
            ]
        else:
            logger.warning(f"未配置年份 {current_year} 的GridView映射")
            return True, base_html

        logger.info(f"开始限制分页爬取，当年({current_year})GridView列表: {current_year_grid_views}")

        # 如果没有提供school_id，则返回第一页内容
        if school_id is None:
            logger.warning("未提供school_id，返回第一页内容")
            return True, base_html

        # 使用多线程处理当年的GridView
        import concurrent.futures
        import threading

        # 创建线程锁
        thread_lock = threading.Lock()
        results = {}

        def process_grid_view(grid_view_id):
            try:
                with thread_lock:
                    logger.info(f"开始处理 {grid_view_id}")

                success, total_pages = crawl_first_and_last_page_limited(url, grid_view_id, session, school_id)

                with thread_lock:
                    results[grid_view_id] = {
                        'success': success,
                        'total_pages': total_pages
                    }
                    logger.info(f"完成处理 {grid_view_id}: success={success}, total_pages={total_pages}")

            except Exception as e:
                with thread_lock:
                    logger.error(f"处理 {grid_view_id} 时发生错误: {str(e)}")
                    results[grid_view_id] = {
                        'success': False,
                        'total_pages': 0
                    }

        # 使用线程池处理
        with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
            futures = [executor.submit(process_grid_view, grid_view_id) for grid_view_id in current_year_grid_views]
            concurrent.futures.wait(futures)

        # 统计结果
        success_count = sum(1 for result in results.values() if result['success'])
        total_count = len(current_year_grid_views)

        logger.info(f"限制分页爬取完成: 成功 {success_count}/{total_count}")
        logger.info(f"各GridView处理结果: {results}")

        return True, base_html

    except Exception as e:
        logger.error(f"获取页面时发生错误: {str(e)}")
        return False, None
