import requests
import json
import time

# API地址
base_url = "http://localhost:8000/api"

def test_check_login_status():
    """测试检查登录状态API"""
    print("测试检查登录状态API...")

    # 发送请求
    response = requests.get(f"{base_url}/check_login_status")

    # 打印结果
    print(f"状态码: {response.status_code}")
    result = response.json()
    print(json.dumps(result, indent=4, ensure_ascii=False))

    return result

def main():
    """主函数"""
    # 第一次检查登录状态
    print("第一次检查登录状态...")
    result = test_check_login_status()
    is_logged_in = result.get("is_logged_in")

    # 如果未登录且后台正在执行登录，等待一段时间后再次检查
    if not is_logged_in and "后台执行" in result.get("message", ""):
        print("\n登录正在后台执行，等待10秒后再次检查...")
        time.sleep(10)

        # 第二次检查登录状态
        print("\n第二次检查登录状态...")
        result = test_check_login_status()
        is_logged_in = result.get("is_logged_in")

    print(f"\n最终登录状态: {'已登录' if is_logged_in else '未登录'}")

if __name__ == "__main__":
    main()
